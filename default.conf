server {
    listen       80;
    server_name  localhost;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        # index  index.html index.htm;
        try_files $uri @router;
        if ($request_filename ~* .*\.(?:htm|html)$)
        {
            add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
        }

    }
    location @router {
    rewrite ^.*$ /app/h5-bj-fan-festival/index.html last; ## rewrite到/app/vue/index.html
  }

    # location = /index.html {
    #     add_header Cache-Control "no-cache, no-store";
    # }
    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}
