# 全局组件说明文档

## 概述

项目中包含多个全局组件和第三方UI组件，主要分为Vant UI组件库和自定义业务组件两大类。所有组件都支持TypeScript，并采用Vue Class Component语法。

## Vant UI组件库

### 全局注册的Vant组件

在 `main.ts` 中全局注册了以下Vant组件：

```typescript
import {
  Toast, Loading, Button, CountDown, Icon, Field,
  Swipe, SwipeItem, Picker, Popup, List, PullRefresh,
  Checkbox, Dialog
} from 'vant';
import { Image as VanImage } from 'vant';

Vue.use(Toast).use(Loading).use(VanImage).use(Button)
   .use(Icon).use(Field).use(Swipe).use(SwipeItem)
   .use(Picker).use(Popup).use(List).use(PullRefresh)
   .use(CountDown).use(Checkbox);
```

### Toast 全局配置

```typescript
Toast.setDefaultOptions({
  forbidClick: true,  // 背景不可点击
  overlay: true       // 显示背景遮罩层
});
```

## 自定义全局组件

### 1. Modal 模态框组件

**文件路径**: `src/components/modal/Modal.vue`

#### Props
```typescript
interface ModalProps {
  position?: string;        // 弹窗位置，默认'center'
  bgObj?: object;          // 背景图属性对象
  showCloseButton?: boolean; // 是否显示关闭按钮，默认true
}
```

#### Events
```typescript
interface ModalEvents {
  close(): void;  // 关闭弹窗时触发
}
```

#### Slots
```typescript
interface ModalSlots {
  default: any;  // 弹窗内容插槽
}
```

#### 使用示例
```vue
<template>
  <modal 
    :bg-obj="{ background: 'https://example.com/bg.png' }"
    :show-close-button="true"
    @close="handleClose"
  >
    <div>弹窗内容</div>
  </modal>
</template>

<script lang="ts">
import Modal from '@/components/modal/Modal.vue';

@Component({
  components: { Modal }
})
export default class MyComponent extends Vue {
  handleClose() {
    console.log('弹窗关闭');
  }
}
</script>
```

#### 特性
- 自动处理背景滚动锁定
- 支持自定义背景图片
- 响应式尺寸适配
- 防止背景穿透

### 2. Loading 加载组件

**文件路径**: `src/components/loading/Loading.vue`

#### 使用示例
```vue
<template>
  <loading v-if="showLoading" />
</template>

<script lang="ts">
import Loading from '@/components/loading/Loading.vue';

@Component({
  components: { Loading }
})
export default class MyComponent extends Vue {
  showLoading = false;
}
</script>
```

#### 特性
- 9宫格动画效果
- 全屏遮罩
- 自定义加载文案
- 防止用户操作

### 3. ErrModal 错误提示弹窗

**文件路径**: `src/components/modal/errModal.vue`

#### Props
```typescript
interface ErrModalProps {
  pageTab?: any;  // 页面标识
}
```

#### Methods
```typescript
interface ErrModalMethods {
  open(obj: ErrorConfig): void;  // 打开错误弹窗
}
```

#### 错误配置对象
```typescript
interface ErrorConfig {
  tips: string;      // 错误提示文案
  showBtn: boolean;  // 是否显示按钮
  btnTxt?: string;   // 按钮文案
  flag?: string;     // 错误标识
}
```

#### 使用示例
```vue
<template>
  <err-modal ref="errRef" @locationJudge="handleLocation" />
</template>

<script lang="ts">
import ErrModal from '@/components/modal/errModal.vue';

@Component({
  components: { ErrModal }
})
export default class MyComponent extends Vue {
  @Ref('errRef') errRef!: ErrModal;

  showError() {
    this.errRef.open({
      tips: '网络异常，请稍后重试',
      showBtn: true,
      btnTxt: '确定',
      flag: 'network'
    });
  }

  handleLocation() {
    console.log('处理定位逻辑');
  }
}
</script>
```

### 4. Background 背景组件

**文件路径**: `src/components/background/Background.vue`

#### Props
```typescript
interface BackgroundProps {
  layoutType?: 'onePageTop' | 'imageSizeTop' | 'onePageMiddle';  // 布局类型
  pageHeight?: number;    // 页面高度(px)
  bgImage?: string | string[];  // 背景图片URL
  bgColor?: string;       // 背景颜色，默认'#fff'
}
```

#### 布局类型说明
- `onePageTop`: 一屏贴顶布局，背景图片750*1496
- `imageSizeTop`: 贴顶可滚动布局，图片高度可超过一屏
- `onePageMiddle`: 一屏居中布局（已废弃）

#### Slots
```typescript
interface BackgroundSlots {
  default: any;   // 热区定位容器内容
  content: any;   // 自定义内容区域
}
```

#### 使用示例
```vue
<template>
  <background v-bind="bgLayout">
    <!-- 热区内容 -->
    <div class="hot-area">...</div>
  </background>
</template>

<script lang="ts">
import Background from '@/components/background/Background.vue';

@Component({
  components: { Background }
})
export default class MyComponent extends Vue {
  get bgLayout() {
    return {
      layoutType: 'onePageTop',
      bgImage: ['https://example.com/bg.jpg'],
      bgColor: '#F75476'
    };
  }
}
</script>
```

#### 特性
- 自动设置html/body背景色
- 支持多张背景图片叠加
- 热区定位容器自动铺满
- 组件销毁时自动清理背景色

### 5. BackButton 返回按钮组件

**文件路径**: `src/components/back/index.vue`

#### Props
```typescript
interface BackButtonProps {
  isClose?: boolean;  // 是否为关闭按钮模式
}
```

#### 使用示例
```vue
<template>
  <backButton :is-close="true" />
</template>

<script lang="ts">
import BackButton from '@/components/back/index.vue';

@Component({
  components: { BackButton }
})
export default class MyComponent extends Vue {}
</script>
```

## 业务模态框组件

### 1. RegisterModal 注册弹窗

**文件路径**: `src/components/commonModal/registerModal.vue`

#### Props
```typescript
interface RegisterModalProps {
  // 无外部props，通过open方法传入数据
}
```

#### Methods
```typescript
interface RegisterModalMethods {
  open(obj: GameObj): void;  // 打开注册弹窗
}
```

#### 数据结构
```typescript
interface GameObj {
  mobile: string;    // 手机号
  tool_num: number;  // 工具数量
}
```

#### 使用示例
```vue
<template>
  <register-modal 
    ref="registerModal"
    @backRegister="handleBackRegister"
    @bindSuccess="handleBindSuccess"
  />
</template>

<script lang="ts">
import RegisterModal from '@/components/commonModal/registerModal.vue';

@Component({
  components: { RegisterModal }
})
export default class MyComponent extends Vue {
  @Ref('registerModal') registerModal!: RegisterModal;

  openRegister() {
    this.registerModal.open({
      mobile: '138****8888',
      tool_num: 2
    });
  }

  handleBackRegister() {
    console.log('返回注册');
  }

  handleBindSuccess() {
    console.log('绑定成功');
  }
}
</script>
```

#### 特性
- 集成滑块验证码
- 手机号验证
- 短信验证码倒计时
- 表单验证

### 2. ConfirmModal 确认弹窗

**文件路径**: `src/components/commonModal/confirmModal.vue`

#### Events
```typescript
interface ConfirmModalEvents {
  backRegister(): void;  // 返回注册
  bindSuccess(): void;   // 绑定成功
}
```

#### 使用示例
```vue
<template>
  <confirm-modal 
    ref="confirmModal"
    @backRegister="backToRegister"
    @bindSuccess="onBindSuccess"
  />
</template>
```

### 3. AddressModal 地址弹窗

**文件路径**: `src/components/commonModal/addressModal.vue`

用于收货地址填写和提交。

## 第三方组件集成

### 1. Verify 验证码组件

来自 `@bangdao/captcha-multi` 包：

```vue
<Verify
  @success="onVerifySuccess"
  :mode="'pop'"
  :vSpace="10"
  baseUrl="https://oapi.bangdao-tech.com/gateway.do"
  :captchaType="'blockPuzzle'"
  :imgSize="{ width: '280px', height: '160px' }"
  ref="verify"
/>
```

## 全局方法和属性

### Vue原型扩展

在 `main.ts` 中扩展了Vue原型：

```typescript
// 全局方法
Vue.prototype.$publics = publics;           // 公共方法库
Vue.prototype.$getImgSize = getImgSize;     // 图片尺寸处理
Vue.prototype.$microUtils = microUtils;     // 微应用工具
Vue.prototype.$configData = configData;     // 配置数据
Vue.prototype.$versionNum = versionNum;     // 版本信息
```

### 使用示例
```typescript
export default class MyComponent extends Vue {
  mounted() {
    // 使用全局图片处理方法
    const imgStyle = this.$getImgSize('https://example.com/image_w100_h200.png');
    
    // 使用微应用工具
    const token = this.$microUtils.getToken('Token');
    
    // 使用配置数据
    const config = this.$configData.homePage;
  }
}
```

## 组件开发规范

1. **命名规范**: 使用PascalCase命名组件
2. **类型定义**: 为Props、Events、Slots提供完整类型定义
3. **文档注释**: 为组件方法添加JSDoc注释
4. **样式隔离**: 使用scoped样式避免样式污染
5. **响应式设计**: 使用rem单位适配不同屏幕
6. **错误处理**: 组件内部要有完善的错误处理机制
