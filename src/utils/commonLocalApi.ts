import Vue from 'vue';
import EventBus from '@/utils/evnetBus';
import { myTrackEvent, GATEWAY_TYPE } from '@/common/xmp';
import { Toast } from 'vant';
import { doDecrypt, doEncrypt } from '@/utils/sm2';

const sm3 = require("sm-crypto").sm3;
/**
 * 通用接口参数封装函数
 * @param data 业务参数对象
 * @param accessToken 可选的 accessToken，如果不提供则自动获取
 * @returns 封装后的完整参数对象
 */
export async function commonApiInfo(data: any, accessToken?: String) {
  const microUtils = (Vue.prototype as any).$microUtils;
  const body = doEncrypt(JSON.stringify(data));
  const sign = sm3(JSON.stringify(data));
  console.log('🚀 ~ file:commonLocalApi method:commonApiInfo line:18 -----', body ,sign);
  const param = {
    url: '', // 接口路径
    AnalogData: '', // 本地文件地址/名称
    AnalogDataname: '通用接口获取', // 方法或接口描述
    data: {
      url: '/osg-open-pt0001/member/c3/1202516',
      type: '01',
      accessToken: accessToken || microUtils.getAccessToken() || '--',
      data: {
        // 业务入参
        target: '14101', //"5位省码",
        source: '38141010003', //"渠道编码member",
        serviceCode: '0103224', //"接口编码",
        data: {
          // 添加通用认证参数
          authToken: microUtils.getToken('authToken') || '',
          token: microUtils.getToken('Token') || '',
          method: data.method,
          // 合并传入的业务参数
          data: {
            encryData: body,
            sign,
          }
        }
      }
    }
  };
  console.log(
    '🚀 ~ file:commonLocalApi method:commonApiInfo line:31 -----',
    param
  );
  return param;
}

/**
 * 处理 NLRequest 响应数据
 * 参考 localPromiseWapper 的处理逻辑，确保响应处理的完整性和一致性
 * @param res NLRequest 的原始响应数据
 * @param params 请求参数，用于错误埋点
 * @returns [data, error] 统一格式的返回值
 */
export async function handleNLRequestResponse(
  res: any,
  params?: any
): Promise<[any, any]> {
  console.log('🚀 ~ file:commonLocalApi method:handleNLRequestResponse -----', res);

  try {
    // 清除 Toast（参考 localPromiseWapper）
    Toast.clear();

    // NLRequest 响应结构：{ code: "1", data: {...} }
    if (res && res.code) {
      console.log(res, 'NLRequest响应数据');
      let responseData = res.data || {};
      // 成功时处理数据解密（如果需要）
      // 检查是否需要解密（参考 localPromiseWapper 的解密逻辑）
      if (responseData?.encryData) {
        try {
          responseData = await doDecrypt(responseData.encryData);
          console.log('🚀 ~ 解密后数据 -----', responseData);
        } catch (decryptError) {
          console.error('响应数据解密失败:', decryptError);
          // 解密失败时使用原始数据
          // responseData = responseData?.data;
        }
      }
      if (res.code == '1' && responseData?.rtnFlag === 9999) {
        const {  rtnData } = responseData;
        // 成功时返回数据部分
        console.info(
          '%c NLRequest success',
          'color: #68C174; font-weight: bold',
          responseData ,rtnData
        );
        return [rtnData, undefined];
      } else {
        // 业务异常处理
        const microUtils = (Vue.prototype as any).$microUtils;
        const configData = (Vue.prototype as any).$configData;

        // 检查是否为登录失效场景（参考 localPromiseWapper）
        if (
          configData &&
          (responseData.code == configData.code ||
            responseData.code == configData.otherCode)
        ) {
          EventBus.$emit('showBounced');
          return [undefined, undefined] as any;
        }

        // 错误埋点上报
        myTrackEvent('BizError', {
          type: GATEWAY_TYPE,
          method: params?.method || 'NLRequest',
          code: responseData?.rtnFlag,
          message:
            responseData?.message || responseData?.msg || 'NLRequest接口异常',
          biz_content: GATEWAY_TYPE
        });
        console.log('🚀 ~ file:commonLocalApi method:handleNLRequestResponse line:110异常 -----', responseData);
        // 返回错误信息
        const errorInfo = {
          rtn_flag: responseData?.rtnFlag || 'fail',
          rtn_msg: responseData?.message || responseData?.msg || responseData?.rtnMsg || '接口请求失败'
        };
        return [undefined, errorInfo];
      }
    } else {
      // 响应格式异常
      myTrackEvent('BizError', {
        type: GATEWAY_TYPE,
        method: params?.method || 'NLRequest',
        code: 'response_format_error',
        message: 'NLRequest响应格式异常',
        biz_content: GATEWAY_TYPE
      });

      return [
        undefined,
        {
          rtn_flag: 'response_error',
          rtn_msg: res?.message || '不好意思，出错啦\n请稍后再来～～'
        }
      ];
    }
  } catch (error) {
    console.error('🚀 ~ handleNLRequestResponse error -----', error);

    // 异常埋点上报
    myTrackEvent('BizError', {
      type: GATEWAY_TYPE,
      method: params?.method || 'NLRequest',
      message: error || '响应处理异常',
      biz_content: GATEWAY_TYPE
    });

    console.info(
      '%c NLRequest failed',
      'color: #DC143C; font-weight: bold',
      error
    );

    return [
      undefined,
      {
        rtn_flag: 'process_error',
        rtn_msg: '响应处理异常，请稍后重试'
      }
    ];
  } finally {
    // 延迟清除 Toast（参考 localPromiseWapper）
    setTimeout(() => {
      Toast.clear();
    }, 1000);
  }
}

/**
 * 通用 JSBridge 接口调用函数
 * 集成参数封装和响应处理
 * @param data 业务参数对象
 * @param accessToken 可选的 accessToken
 * @returns Promise<[data, error]> 统一格式的返回值
 *
 * @example
 * // 使用方式一：预定义方法调用（推荐）
 * import { apiMethods } from '@/utils/commonLocalApi';
 * const [res, err] = await apiMethods.getActivityList({ actId: 'xxx' });
 *
 * // 使用方式二：创建自定义方法
 * import { createApiMethod } from '@/utils/commonLocalApi';
 * const getActivityListApi = createApiMethod('getActivityList');
 * const [res, err] = await getActivityListApi({ actId: 'xxx' });
 *
 * // 使用方式三：原始调用方式（兼容旧代码）
 * const [res, err] = await callNLRequestApi({
 *   method: '10002',
 *   actId: 'xxx'
 * });
 */
/**
 * API 方法映射表
 * 将业务方法名映射到对应的 method 编号
 */
export const API_METHOD_MAP = {
  // 基础功能
  getAuthToken: '10001',        // 获取国网sgcc授权
  getActivityList: '10002',     // 获取活动列表
  getTaskList: '10003',         // 通用获取任务列表
  completeTask: '10004',        // 通用任务完成
  getActivityCode: '10005',     // 获取每个轮次的抽奖码信息
  reportUser: '10006',          // 活动用户上报
  getMyPrize: '10007',          // 我的奖品查询

} as const;

/**
 * 创建带有预设 method 的 API 调用函数
 * @param methodKey API_METHOD_MAP 中的方法名
 * @returns 返回一个预设了 method 的 API 调用函数
 *
 * @example
 * // 创建获取活动列表的 API 函数
 * const getActivityListApi = createApiMethod('getActivityList');
 * const [res, err] = await getActivityListApi({ actId: 'xxx' });
 */
export function createApiMethod(methodKey: keyof typeof API_METHOD_MAP) {
  const method = API_METHOD_MAP[methodKey];

  return async function(data: object = {}, accessToken?: string): Promise<[any, any]> {
    return await callNLRequestApi({
      method,
      ...data
    }, accessToken);
  };
}

/**
 * 预定义的常用 API 方法
 * 可以直接使用，无需手动指定 method
 */
export const apiMethods = {
  // 基础功能
  getAuthToken: createApiMethod('getAuthToken'),
  getActivityList: createApiMethod('getActivityList'),
  getTaskList: createApiMethod('getTaskList'),
  completeTask: createApiMethod('completeTask'),
  getActivityCode: createApiMethod('getActivityCode'),
  reportUser: createApiMethod('reportUser'),
  getMyPrize: createApiMethod('getMyPrize'),
};

export async function callNLRequestApi(
  data: object,
  accessToken?: String
): Promise<[any, any]> {
  try {
    // 获取 Vue 实例以访问 $publics
    const vueInstance = Vue.prototype as any;

    // 封装参数
    const params = await commonApiInfo(data, accessToken);

    // 调用 JSBridgeMethod
    const res = await vueInstance.$publics.JSBridgeMethod('NLRequest', params);

    // 处理响应，传递原始参数用于错误埋点
    return await handleNLRequestResponse(res, data);
  } catch (error) {
    console.error(
      '🚀 ~ file:commonLocalApi method:callNLRequestApi error -----',
      error
    );

    // 网络异常埋点上报
    myTrackEvent('BizError', {
      type: GATEWAY_TYPE,
      method: (data as any)?.method || 'NLRequest',
      message: error || '网络异常',
      biz_content: GATEWAY_TYPE
    });

    // 网络异常或其他错误
    return [
      undefined,
      {
        rtn_flag: 'network_error',
        rtn_msg: '网络异常，请稍后重试'
      }
    ];
  }
}
