// 图片加载相关 ========================================================================

/**
 * 图片加载完成监听
 * @param {String[] | String} imgUrls
 * @param {() => void} [callback] 图片加载完成回调
 * @returns {Promise} 图片加载完成
 */
export function imageLoadedHandle(
  imgUrls: string[] | string,
  callback: () => void
) {
  return new Promise<void>(resolve => {
    const imgUrlList = (Array.isArray(imgUrls) ? imgUrls : [imgUrls]).filter(
      item => !!item
    )
    const len = imgUrlList.length
    if (len === 0) {
      if (callback && typeof callback === 'function') {
        callback()
      }
      resolve()
      return
    }
    let n = 0
    for (let i = 0; i < len; i++) {
      if (imgUrlList[i]) {
        // 创建一个Image对象，实现图片的预下载
        const img = new Image()
        img.onload = function onload() {
          this.onload = null
          n++
          if (n === len) {
            if (callback && typeof callback === 'function') {
              callback()
            }
            resolve()
          }
        }
        img.src = imgUrlList[i]
      } else {
        n++
      }
    }
  })
}

/**
 * 入参一个图片地址列表，列表，创建 image 实例，做预加载
 * @param {String[]} imgList 图片列表
 */
export function preLoadImage(imgList: string[]) {
  const imgs = imgList || []
  const loop = function loops(i:any) {
    const img = imgs[i]
    setTimeout(() => {
      new Image().src = img
    }, 200 * i)
  }

  for (let i = 0, len = imgs.length; i < len; i++) {
    loop(i)
  }
}
