[vue-cli脚手架使用](https://cli.vuejs.org/zh/config/#%E5%85%A8%E5%B1%80-cli-%E9%85%8D%E7%BD%AE)
---

    # install dependencies
    npm install

    # serve with hot reload at localhost:8080
    npm run serve

    # build for production with minification
    npm run build

依赖包含bangdao/common,安装依赖需要连接vpn
<br />


### 更新日志
---
版本：1.0.2
更新时间： 20210312
>   + 新增mock功能，在service/http中开启
>   + 新增KubeEase自动发布需要文件 Dockerfile、default.conf
>   + 新增获取中台广告位接口示例
>   + 去除@bangdao/vue-h5-component依赖，将错误页组件result放在src/components
>   + 修改router.js中错误页的名称为ErrorPage，为了解决node_modules中http请求失败跳转错误页的问题
>   + 完善工程目录，处理一些TS规范、文件名等细节问题
<br />

### 开发注意点

> + 注意router.js中错误页的路由name为ErrorPage
> + 在链接后面增加 debug=true 可开启c端调试工具
> + 在service/http中开启关闭mock功能
> + 不使用KubeEase发布，可以删除default.con和Dockerfile文件
