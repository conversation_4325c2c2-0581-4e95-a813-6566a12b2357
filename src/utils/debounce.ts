/**
 * promise 节流装饰器
 * @param time 节流事件
 * @param delayTime 延迟释放锁时间，一般是用于页面跳转
 * @constructor
 */
function debounce(delay: number = 300) {
  let timer: number | null;

  return function(target: any, key: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function(...args: any[]) {
      if (timer) clearTimeout(timer);
      timer = window.setTimeout(() => {
        originalMethod.apply(this, args);
      }, delay);
    };

    return descriptor;
  };
}

/**
 * promise 节流函数版本，使用参考测试用例
 * @param fn
 * @param time
 * @param delayTime
 * @constructor
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
function promiseDebounce(fn, time = 1500, delayTime = 2000) {
  let status = '';
  return function() {
    if (status === 'lock') return;
    status = 'lock';
    try {
      const args = Array.prototype.slice.call(arguments);
      const data = Promise.all([
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        fn.apply(this, args),
        new Promise(resolve => {
          setTimeout(resolve, time);
        })
      ]);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      return data[0];
    } finally {
      setTimeout(() => {
        status = '';
      }, delayTime);
    }
  };
}

/**
 * 页面跳转装饰器
 * @param delayTime
 */
function jumpTime(delayTime = 500) {
  return debounce(delayTime);
}

/**
 * 页面跳转函数
 * @param fn
 * @param delayTime
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
function jumpTimeFn(fn, delayTime = 200) {
  return promiseDebounce(fn, 0, delayTime);
}

export { debounce, promiseDebounce, jumpTime, jumpTimeFn };
