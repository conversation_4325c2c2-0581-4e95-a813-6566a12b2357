<!-- 顶部返回键-->
<template>
  <div class="goBack" :class="[isClose ? 'close-act' : '']">
    <div
      class="backImg"
      :style="{
        backgroundImage: `url('${
          isClose ? $configData.retrunOpt.homeBg : $configData.retrunOpt.subBg
        }')`,
        width: `${
          isClose
            ? $configData.retrunOpt.homeBgWidth
            : $configData.retrunOpt.subBgWidth
        }`
      }"
      @click="goBack"
    ></div>
    <div class="back-tit">{{ title }}</div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class BackButton extends Vue {
  @Prop({
    type: Boolean,
    default: false
  })
  isClose: boolean | undefined; // 默认为 “返回上一级页面”

  @Prop({
    type: String,
    default: ''
  })
  title: '' | undefined; // 标题

  @Prop({
    type: String,
    default: ''
  })
  pathName: '' | undefined; // 标题

  goBack() {
    if (this.isClose) {
      // eslint-disable-next-line no-undef
      WSGWBridge.callNativeFunction('close', {}, function callback(
        response: any
      ) {
        console.log('返回', response);
      });
    } else if (this.pathName) {
      this.$router.push({
        path: '/' + this.pathName
      });
    } else {
      this.$router.go(-1);
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  opacity: 1;
}
.close-act {
  padding-top: 0.14rem;
}
</style>
