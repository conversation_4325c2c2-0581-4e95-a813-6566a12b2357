<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import { lotteryImg } from '@/utils/constant';
import Modal from '@/components/modal/Modal.vue';
import RegisterModal from '@/components/commonModal/registerModal.vue';
import { jumpTime } from '@/utils/debounce';
import { userService } from '@/service/api';
import { sm2Encrypt } from '@/common/sm2';
import { globalConfig } from '@/utils/config';

@Component({
  components: { RegisterModal, Modal },
  computed: {
    lotteryImg() {
      return lotteryImg;
    }
  }
})
export default class confirmModal extends Vue {
  @Ref('confirmModal') confirmModal: any;
  @Ref('registerModal') registerModal: any;

  gameObj: any = {
    mobile: '',
    tool_num: 0,
    code: ''
  };

  open(gameObj: any) {
    console.log('🚀 ~ file:confirmModal method:open line:25 -----', gameObj);
    this.confirmModal.show = true;
    this.gameObj.mobile = gameObj.mobile;
    this.gameObj.code = gameObj.code;
  }

  /**
   * 手机号处理
   */
  get newMobile() {
    const mobile = (this.gameObj.mobile &&
      `${this.gameObj.mobile.substr(0, 3)}-${this.gameObj.mobile.substr(
        -3,
        3
      )}`.split('-')) || ['', ''];
    return mobile;
  }

  @jumpTime()
  async confirm() {
    const { activity_id, errDialogConfig } = this.$configData.homePage;
    const [, err] = await userService.userBind({
      award_mobile: sm2Encrypt(this.gameObj.mobile),
      sms_code: this.gameObj.code,
      activity_id: activity_id,
      token:
        this.$microUtils.getToken('Token') || globalConfig.httpConfig.token,
      item_type: 'SPFAN',
      combine: 'SPFAN'
    });
    console.log(
      '🚀 ~ file:confirmModal method:confirm line:61 -----',
      errDialogConfig
    );
    if (err) {
      const { rtn_msg, rtn_flag } = err;
      const flag = errDialogConfig.find((item: any) => item.code === rtn_flag)
        ?.tips;
      return this.$toast(flag ?? rtn_msg ?? '系统繁忙，请稍候再试');
    }
    this.$toast('绑定成功');
    this.$emit('bindSuccess');
    this.confirmModal.show = false;
  }

  backRegister() {
    this.$emit('backRegister');
  }
}
</script>

<template>
  <div>
    <!--确认手机信息弹窗-->
    <modal
      class="confirmRef"
      :bg-obj="{ background: lotteryImg.confirmMobileDialog }"
      ref="confirmModal"
      :show-close-button="false"
    >
      <div class="confirmRef__phone">
        {{ newMobile[0] + '****' + newMobile[1] }}
      </div>
      <!--    <div class="confirmRef__tip">-->
      <!--      温馨提示：提交后不可更改-->
      <!--    </div>-->
      <div
        @click="
          confirmModal.show = false;
          backRegister();
        "
        class="close_w_h close"
      ></div>
      <div @click.stop="confirm" class="confirmRef__button right"></div>
    </modal>
  </div>
</template>

<style scoped lang="less">
.confirmRef {
  &__phone {
    font-family: Alibaba PuHuiTi;
    font-size: 0.56rem;
    font-weight: bold;
    font-stretch: normal;
    color: #383838;
    position: absolute;
    top: 3.04rem;
    left: 1.15rem;
  }

  .close {
    position: absolute;
    top: 0;
    right: 0.5rem;
  }

  &__button {
    width: 3.78rem;
    height: 1.01rem;
    position: absolute;
    left: 1.4rem;
    top: 5.34rem;

    //&.right {
    //  left: 3.4rem;
    //}
  }
}
</style>
