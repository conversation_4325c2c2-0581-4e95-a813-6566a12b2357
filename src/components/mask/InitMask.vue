<template>
  <div class="mask white" :style="{ backgroundColor: maskBgColor }">
    <div class="bd-loading">
      <div class="bd-loading-indicator">
        <div class="bd-loading-item" :style="{ '--color': loadingColor }"></div>
        <div class="bd-loading-item" :style="{ '--color': loadingColor }"></div>
        <div class="bd-loading-item" :style="{ '--color': loadingColor }"></div>
      </div>
      <div class="bd-loading-text">{{ text }}</div>
      <slot />
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component({})
export default class InitMask extends Vue {
  // 加载点的颜色
  @Prop({
    type: String,
    default: '#108ee9',
  })
  loadingColor!: string

  // 背景颜色
  @Prop({
    type: String,
    default: '#fff',
  })
  maskBgColor!: string

  // 文案
  @Prop({
    type: String,
    default: '加载中...',
  })
  text!: string
}
</script>
<style scoped lang="less">
/*loading全屏遮罩*/
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #f5f5f9;
  z-index: 999;
  perspective: 5000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/*loading页*/
.bd-loading {
  margin-bottom: 100px;
  text-align: center;
  color: #888;
  padding: 20px 0;
  font-size: 12px;
  line-height: 22px;
  min-height: 22px;
}
.bd-loading-indicator .bd-loading-item {
  width: 10px;
  height: 10px;
  margin: 0;
}
.bd-loading-indicator .bd-loading-item:before {
  content: ' ';
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -6.38px;
  margin-top: -9.56px;
  width: 12.75px;
  height: 19.13px;
  -webkit-transform: skew(-0.46rad) scale(0.5);
  transform: skew(-0.46rad) scale(0.5);
  -webkit-transform-origin: center center;
  transform-origin: center center;
  border-radius: 1px 2px 2px 3px;
}
.bd-loading-indicator .bd-loading-item:before {
  background-color: var(--color);
}
.bd-loading-indicator.blue .bd-loading-item:before {
  background-color: #108ee9;
}
.bd-loading-indicator.white .bd-loading-item:before {
  background-color: #fff !important;
}
.bd-loading-indicator .bd-loading-item {
  display: inline-block;
  position: relative;
  -webkit-transform: scale(0);
  transform: scale(0);
  opacity: 0;
  -webkit-animation: AULoadingScaleColorBoth 0.766s
    cubic-bezier(0.42, 0, 0.58, 1) infinite alternate;
  animation: AULoadingScaleColorBoth 0.766s cubic-bezier(0.42, 0, 0.58, 1)
    infinite alternate;
}
.bd-loading-indicator .bd-loading-item:nth-child(1) {
  -webkit-animation-delay: -2.2s;
  animation-delay: -2.2s;
}
.bd-loading-indicator .bd-loading-item:nth-child(2) {
  -webkit-animation-delay: -1.966s;
  animation-delay: -1.966s;
}
.bd-loading-indicator .bd-loading-item:nth-child(3) {
  -webkit-animation-delay: -1.666s;
  animation-delay: -1.666s;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes AULoadingScaleColorBoth {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  34.7826087% {
    transform: scale(0.12);
    opacity: 0.001;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes AULoadingScaleColorBoth {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }
  34.7826087% {
    -webkit-transform: scale(0.12);
    opacity: 0.001;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 1;
  }
}
</style>
