<template>
  <div class="rules">
    <!-- 顶部返回按钮 -->
    <backButton
      :isCover="isCover"
      :bgColor="bgColor"
      :back-icon="IMAGE.BACK"
      :path-name="pathName"
    ></backButton>
    <!-- 活动规则链接 -->
    <iframe
      :style="{
        minHeight: 'calc(100vh - ' + `${isCover ? '0' : '0.84'}` + 'rem)'
      }"
      width="100%"
      frameborder="0"
      :src="url"
    ></iframe>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import BackButton from '@/components/IframeBack/index.vue';
import { IMAGE } from '@/utils/constant';

@Component({
  computed: {
    IMAGE() {
      return IMAGE;
    }
  },
  components: { BackButton }
})
export default class Rules extends Vue {
  rulesUrl: any = ''; // 活动规则链接

  url: any = ''; // 链接
  isCover = false;
  bgColor: any = '';

  pathName = 'home';
  mounted() {
    // 配置项
    console.log('跳转链接:', this.$route.query.url);
    const { isCover, bgColor, url } = this.$route.query;
    this.isCover = isCover === 'T' ? true : false;
    this.bgColor = bgColor;
    this.url = url;
    this.rulesUrl = this.$route.query.url;
  }
}
</script>
