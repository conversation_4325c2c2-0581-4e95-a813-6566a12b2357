import { Vue } from "vue-property-decorator";

import { Http, getIn } from "@bangdao/apollo";

import { IHttpRequest } from "@bangdao/apollo/es/type";
import { globalConfig } from "@/utils/config";
import EventBus from "@/utils/evnetBus";
import {Toast} from "vant";
import RemoteApiService from "@/service/remoteApiService";
import LocalHttpService from "@/service/localHttpService";


const http = new Http(globalConfig.httpConfig);
console.log("----------http", http);
export default class BaserService extends Vue {
  private _remoteApi?: RemoteApiService;
  private _localHttp?: LocalHttpService;

  private get $remoteApi(): RemoteApiService {
    if (!this._remoteApi) {
      this._remoteApi = new RemoteApiService({
        configData: (this as any).$configData,
        microUtils: (this as any).$microUtils,
      });
    }
    return this._remoteApi;
  }

  private get $localHttp(): LocalHttpService {
    if (!this._localHttp) {
      this._localHttp = new LocalHttpService({
        configData: (this as any).$configData,
        microUtils: (this as any).$microUtils,
      });
    }
    return this._localHttp;
  }

  // 远程API请求配置
  private remoteApiConfig = {
    retryCount: 0,
    retryDelay: 600,
    cache: false,
    cacheTTL: 30_000,
    showLoading: true,
    loadingText: "加载中...",
    timeout: 30000,           // 默认30秒超时
    enableTimeoutRetry: true, // 默认启用超时重试
  };

  // 远程API请求缓存
  private remoteCache = new Map<string, { t: number; data: any }>();

  /**
   * 远程网关接口请求 - 完整版本
   * @param options 请求参数
   * @param config 可选配置 { retryCount?, retryDelay?, cache?, cacheTTL?, showLoading?, loadingText?, signal?, timeout?, enableTimeoutRetry? }
   * @protected
   */
  protected post(options: IHttpRequest, config?: {
    retryCount?: number;
    retryDelay?: number;
    cache?: boolean;
    cacheTTL?: number;
    showLoading?: boolean;
    loadingText?: string;
    signal?: AbortSignal;
    headers?: Record<string, string>;
    timeout?: number;
    enableTimeoutRetry?: boolean;
  }): Promise<any> {
    return this.$remoteApi.post(options, config);
  }
  /**
   * 配置远程API请求默认参数
   * @param config 配置项
   * @protected
   */
  protected configRemoteApi(config: {
    retryCount?: number;
    retryDelay?: number;
    cache?: boolean;
    cacheTTL?: number;
    showLoading?: boolean;
    loadingText?: string;
    timeout?: number;
    enableTimeoutRetry?: boolean;
  }): void {
    this._remoteApi = this.$remoteApi.withConfig(config);
  }

  /**
   * 清空远程API请求缓存
   * @protected
   */
  protected clearRemoteCache(): void {
    // 通过重新创建实例来清空缓存
    this._remoteApi = undefined;
  }

  /**
   * 清理所有 loading 状态
   * @protected
   */
  protected clearAllLoading(): void {
    this.$remoteApi.clearAllLoading();
  }

  /**
   * 取消所有活跃请求
   * @protected
   */
  protected cancelAllRequests(): void {
    this.$remoteApi.cancelAllRequests();
  }



  /**
   * 本地化接口拦截器
   * @param options
   * @protected
   */
  /**
   * 本地化接口拦截器
   * @param options 请求参数
   * @protected
   */
  protected localPost(options: IHttpRequest): Promise<any> {
    return this.$localHttp.localPost(options);
  }


  /**
   * get 请求
   * @param options options 请求入参。详情通 post
   */
  protected async get(options: IHttpRequest): Promise<[any, any]> {
    return new Promise((resolve) => {
      http
        .get({ ...options, contentType: "form-data" })
        .then((res: any) => {
          const retCode = getIn(res, ["ret_code"]);
          const rtnFlag = getIn(res, ["content", "rtn_flag"]);
          if (retCode !== 200) {
            //   TODO: 跳转异常兜底
            // goToErrorPage(res)
          } else {
            // 网关成功
            if (rtnFlag !== "9999") {
              // goToErrorPage(res)
            } else {
              // 业务成功
              resolve([res, undefined]);
            }
          }
        })
        .catch((err) => {
          console.log(err);

          // uni.hideLoading()
          // goToErrorPage(err)
        });
    });
  }
}
