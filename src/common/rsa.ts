import JSEncrypt from "jsencrypt";

export function getRsaCode(str: any) {
  // 注册加密方法
  const encryptStr = new JSEncrypt();
  const pubKey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHVgYsFDNbmT/zguLTzLbOgk87ZA6h5mTVuBN4pvkVxjmlIECEeNVk9eqmewpjbjSSoVYY1IXIyoiXXmcEdrcomjlAi8U55fdqFPPzByHhGiiGIV+Zx86sKKz6X9mqTfCA7ShLMzKQNfMqnVjpCzGKr7RafPFxc5cIrZnNHT5eoQIDAQAB`;
  encryptStr.setPublicKey(pubKey); // 设置 加密公钥
  const data = encryptStr.encrypt(str.toString()); // 进行加密
  return data;
}
