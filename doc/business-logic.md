# 复杂业务逻辑说明文档

## 概述

本文档详细说明河北省钱行动派项目中的核心业务逻辑，包括活动流程、用户认证、任务系统、锦鲤抽奖、版本控制、加密安全等关键业务模块。

## 项目背景

**项目名称**: 河北省钱行动派
**项目类型**: 国网电力营销活动H5页面
**运行环境**: 国网App内嵌H5页面 (WSGWBridge)

## 核心业务流程

### 1. 应用初始化流程

```mermaid
graph TD
    A[应用启动] --> B[获取配置数据]
    B --> C{维护模式检查}
    C -->|维护中| D[显示维护弹窗并退出]
    C -->|正常| E[创建指纹信息]
    E --> F[初始化微应用工具]
    F --> G[版本控制检查]
    G --> H[加载动态资源]
    H --> I[挂载Vue应用]
```

**实现代码**:
```typescript
// src/main.ts
getConfigData().then((res: any) => {
  const configData = res.data;
  
  // 维护模式检查
  if (configData.maintainConfig?.flag) {
    Dialog.alert({
      message: configData.maintainConfig.text,
      confirmButtonColor: "#108ee9"
    }).then(() => {
      WSGWBridge.callNativeFunction("close", {}, function callback(response: any) {
        console.log("返回", response);
      });
    });
    return;
  }
  
  // 创建指纹并初始化
  createFingerprint().then((result: any) => {
    Vue.prototype.$microUtils = new MicroUtils(configInfo, 'bjFanSeek');
    
    // 版本控制逻辑
    const versionNum = getVersionNumber(result, configData);
    Vue.prototype.$versionNum = versionNum;
    
    // 加载动态资源并启动应用
    loadJsFun(versionNum, configData.pageNameConfig).then(() => {
      new Vue({ router, store, render: (h) => h(App) }).$mount('#app');
    });
  });
});
```

### 2. 用户认证与Token管理

```mermaid
graph TD
    A[用户进入应用] --> B[检查Token有效性]
    B -->|Token有效| C[获取用户信息]
    B -->|Token无效| D[重新获取Token]
    D --> E[Token获取成功]
    E --> C
    C --> F[检查用户绑定状态]
    F -->|已绑定| G[进入活动页面]
    F -->|未绑定| H[显示绑定弹窗]
```

**Token管理实现**:
```typescript
// src/App.vue
export default class extends Vue {
  created(): void {
    // 监听Token回调
    this.$microUtils.customEventBus.$add('tokenCallback', () => {
      this.infoSearch();
    });
    
    // 初始化Token
    this.$microUtils.initToken();
  }
  
  // 用户信息查询
  async infoSearch() {
    const [res, err] = await userService.infoQuery({
      token: this.$microUtils.getToken('Token')
    });
    
    if (err) {
      this.handleTokenError(err);
      return;
    }
    
    // 处理用户信息
    this.handleUserInfo(res);
  }
}
```

### 3. 任务系统业务

```mermaid
graph TD
    A[用户进入任务页] --> B[获取任务列表]
    B --> C[展示任务列表]
    C --> D{用户点击任务}
    D --> E[执行任务跳转]
    E --> F[任务完成回调]
    F --> G[调用完成任务接口]
    G --> H[刷新任务列表]
```

**任务系统实现**:
```typescript
// 获取任务列表
async getTaskList() {
  const [res, err] = await userService.getTaskList({});
  if (err) {
    this.$toast(err.rtn_msg || '获取任务失败');
    return;
  }
  this.taskList = res.taskList || [];
}

// 完成任务
async completeTask(taskId: string) {
  const [res, err] = await userService.completeTask({
    taskId: taskId
  });
  if (err) {
    this.$toast(err.rtn_msg || '任务完成失败');
    return;
  }
  // 刷新任务列表
  this.getTaskList();
}
```

### 4. 锦鲤抽奖业务

```mermaid
graph TD
    A[用户进入抽奖页] --> B[获取活动列表]
    B --> C[获取当前轮次]
    C --> D[获取抽奖码]
    D --> E[展示抽奖信息]
    E --> F{用户参与抽奖}
    F --> G[提交抽奖请求]
    G --> H[展示抽奖结果]
```

**锦鲤抽奖实现**:
```typescript
// 获取活动列表
async getActivityList() {
  const [res, err] = await userService.getActivityList({});
  if (err) return;
  this.activityList = res.list || [];
}

// 获取活动轮次
async getActivityRound() {
  const [res, err] = await userService.getActivityRound({});
  if (err) return;
  this.currentRound = res.round;
}

// 获取抽奖码
async getActivityCode() {
  const [res, err] = await userService.getActivityCode({});
  if (err) return;
  this.lotteryCode = res.code;
}
```

### 5. 版本控制与回滚机制

```mermaid
graph TD
    A[获取用户指纹] --> B{开启版本检查?}
    B -->|是| C[检查版本白名单]
    B -->|否| D[检查回滚配置]
    C -->|在白名单| E[使用白名单版本]
    C -->|不在白名单| F[检查回滚配置]
    F -->|开启回滚| G[使用历史版本]
    F -->|关闭回滚| H[使用当前版本]
    D -->|开启回滚| G
    D -->|关闭回滚| H
    E --> I[加载对应版本资源]
    G --> I
    H --> I
```

**版本控制实现**:
```typescript
// src/main.ts
function getVersionNumber(fingerprint: string, configData: any) {
  const { devConfig, projectConfig } = configData;
  const { versionHistory, versionNumberInfo } = projectConfig;
  
  if (devConfig.checkVersion) {
    const versionWhiteList = devConfig.versionWhiteList;
    const versionIndex = versionWhiteList.findIndex(
      (item: any) => item.versionSign === fingerprint
    );
    
    if (versionIndex > -1) {
      // 使用白名单版本
      return versionWhiteList[versionIndex].versionNumberInfo;
    }
  }
  
  // 根据回滚配置决定版本
  return projectConfig.checkRollback ? versionHistory : versionNumberInfo;
}
```

## 安全机制

### 1. 数据加解密

```mermaid
graph LR
    A[请求数据] --> B[检查加密配置]
    B -->|开启| C[参数加密]
    B -->|关闭| D[直接发送]
    C --> E[发送请求]
    D --> E
    E --> F[接收响应]
    F --> G[检查加密配置]
    G -->|开启| H[响应解密]
    G -->|关闭| I[直接返回]
    H --> J[返回解密数据]
    I --> J
```

**SM2/SM3加解密实现**:
```typescript
// src/utils/sm2.ts
import { sm2, sm3 } from 'sm-crypto';
import { Base64 } from 'js-base64';
import { jsonrepair } from 'jsonrepair';

// SM2加密
export const doEncrypt = (value: any) => {
  const data = '04' + sm2.doEncrypt(value, publicKey, cipherMode);
  return data;
}

// SM2解密
export const doDecrypt = (value: any) => {
  const data = sm2.doDecrypt(value.substr(2), privateKey, cipherMode);
  return JSON.parse(jsonrepair(Base64.decode(data)));
}

// src/service/remoteApiService.ts
// 请求加密流程
const sign = sm3(JSON.stringify(data));  // SM3签名
const encryptedData = doEncrypt(JSON.stringify(data));  // SM2加密

// 响应解密流程
const decryptedData = doDecrypt(encryptedResponse);  // SM2解密
```

### 2. 时间戳校验防重放

```typescript
// src/service/baseService.ts
protected post(options: IHttpRequest): Promise<any> {
  const isShowCheckTime = this.$configData.isShowCheckTime;
  
  if (this.$microUtils.getTimeStamp() && isShowCheckTime) {
    const oldTime: number = this.$microUtils.getTimeStamp();
    const curTime: number = new Date().getTime();
    
    // 30分钟时效检查
    if (Math.abs(curTime - oldTime) > 29 * 60 * 1000) {
      EventBus.$emit("showBounced");
      return [undefined, undefined];
    }
  }
  
  // 继续请求处理...
}
```

## 活动业务逻辑

### 1. 多活动模块管理

项目包含四个主要活动模块：

1. **烤鸭活动** (roast): 报名类活动
2. **炸酱面活动** (noodle): 积分兑换类活动  
3. **艾窝窝活动** (lottery): 抽奖类活动
4. **铜锅活动** (potCopper): 邀请助力类活动

**热区配置**:
```typescript
// src/utils/constant.ts
export const dataList = [
  {
    brief: "烤鸭",
    name: "roast", 
    position: { top: 1289, left: 174, width: 235, height: 63 }
  },
  {
    brief: "炸酱面",
    name: "noodle",
    position: { top: 733, right: 123, width: 235, height: 63 }
  },
  {
    brief: "艾窝窝抽奖", 
    name: "lottery",
    position: { top: 1056, right: 9, width: 235, height: 63 }
  },
  {
    brief: "铜锅",
    name: "potCopper",
    position: { top: 888, left: 26, width: 235, height: 63 }
  }
];
```

### 2. 热区点击处理逻辑

```typescript
// src/pages/home/<USER>
@jumpTime()  // 防抖装饰器
itemClickHandle(item: HotAreaItem): void {
  // 埋点统计
  buriedPoint(item.brief);
  
  const { name, url } = item;
  
  // 规则和服务页面跳转
  if (name === 'rule' || name === 'service') {
    this.$router.push({
      path: '/rule',
      query: {
        url,
        isCover: name === 'service' ? 'T' : ''
      }
    });
    return;
  }
  
  // 奖品页面直接跳转
  if (name === 'prize') {
    this.$router.push({ path: `/${name}` });
    return;
  }
}
```

### 3. 错误处理与用户引导

```typescript
// src/pages/home/<USER>
showErr(err: ApiResponse): void {
  const { rtn_flag, rtn_msg } = err;
  const errObj = this.homeConfig.errDialogConfig?.find(
    (item: any) => item.code === rtn_flag
  );
  
  // 根据配置校验是否绑定户号
  const unBind = true;
  
  if (errObj?.title && unBind) {
    this.errRef.open(errObj);
  } else {
    const errorMessage = errObj?.tips || rtn_msg || '当前人数参与太多,请稍后再试！';
    this.$toast(errorMessage);
  }
}
```

## 工具函数与算法

### 1. 图片尺寸自动计算

```typescript
// src/utils/common.ts
export const getImgSize = (url: any) => {
  const reg = /_w(\d+)_h(\d+)/;
  const result = url.match(reg);
  if (result) {
    return {
      width: `${result[1] / 100}rem`,
      height: `${result[2] / 100}rem`,
    };
  }
};
```

### 2. 防抖装饰器

```typescript
// src/utils/debounce.ts
export function jumpTime(time: number = 1000) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    let timer: any = null;
    
    descriptor.value = function (...args: any[]) {
      if (timer) return;
      
      timer = setTimeout(() => {
        timer = null;
      }, time);
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}
```

### 3. 高德地图定位集成

```typescript
// src/utils/utils.ts
export function getLocation(callback: any, errCallback: any) {
  const mapObj = new AMap.Map("iCenter");
  mapObj.plugin("AMap.Geolocation", function() {
    const geolocation = new AMap.Geolocation({
      enableHighAccuracy: true,
      timeout: 60000,
      maximumAge: 0,
      convert: true,
      showButton: true,
      buttonPosition: "LB",
      buttonOffset: new AMap.Pixel(10, 20),
      showMarker: true,
      showCircle: true,
      panToLocation: true,
      zoomToAccuracy: true
    });
    
    mapObj.addControl(geolocation);
    geolocation.getCurrentPosition();
    
    AMap.event.addListener(geolocation, "complete", onComplete);
    AMap.event.addListener(geolocation, "error", onError);
    
    function onComplete(CitySearchResult: any) {
      if (CitySearchResult && CitySearchResult.position) {
        callback(CitySearchResult);
      } else {
        errCallback(0, "当前位置获取失败，请稍后再次尝试");
      }
    }
    
    function onError(CitySearchResult: any) {
      if (CitySearchResult.info === "FAILED") {
        errCallback();
      }
    }
  });
}
```

## 性能优化策略

### 1. 图片预加载

```typescript
// src/utils/imageHandle.ts
export function preLoadImage(imageUrls: string[]): Promise<void[]> {
  const promises = imageUrls.map(url => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  });
  
  return Promise.all(promises);
}
```

### 2. 路由懒加载

```typescript
// src/router.ts
export default new Router({
  routes: [
    {
      path: '/home',
      name: 'home',
      component: () => import(/*webpackChunkName: "home"*/ './pages/home/<USER>'),
    },
    {
      path: "/link", 
      name: "link",
      component: () => import(/*webpackChunkName: "banner"*/ "./pages/banner/link.vue")
    }
  ],
});
```

## 维护和扩展指南

### 1. 添加新活动模块

1. 在 `src/utils/constant.ts` 中添加热区配置
2. 在 `src/pages/home/<USER>
3. 创建对应的页面组件
4. 在路由中注册新页面

### 2. 扩展API接口

1. 在 `src/service/api.ts` 中添加新的接口方法
2. 遵循现有的参数格式和错误处理模式
3. 添加对应的TypeScript类型定义

**示例**:
```typescript
// src/service/api.ts
public newApiMethod(options: any): Promise<[any, any]> {
  return this.post({
    data: {
      method: '10xxx',  // 接口方法编号
      ...options
    }
  });
}
```

### 3. 安全配置管理

1. SM2/SM3加解密开关通过 `isShowEncryption` 配置控制
2. 时间校验开关通过 `isShowCheckTime` 配置控制
3. 版本控制策略可动态调整
4. 白名单机制支持灵活配置

### 4. 核心API接口说明

| 接口方法 | 方法编号 | 用途 |
|---------|---------|------|
| getAuthToken | 10001 | 获取国网SGCC授权Token |
| getActivityList | 10002 | 获取锦鲤活动列表 |
| getTaskList | 10003 | 获取通用任务列表 |
| completeTask | 10004 | 标记任务完成 |
| getActivityCode | 10005 | 获取抽奖码信息 |
| reportUser | 10006 | 活动参与人数统计埋点 |
| getActivityRound | 10019 | 获取活动轮次信息 |

通过以上业务逻辑的详细说明，开发者可以快速理解河北省钱行动派项目的核心业务流程，并能够有效地进行维护和功能扩展。
