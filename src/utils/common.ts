import {JSBridgeMethod} from "@/common/uap.public";

/**
 * 随机函数
 * @param {number} min
 * @param {number} max
 * @returns
 */
export function randomNum(min: number, max: number) {
    const range = max - min
    const rand = Math.random() //结果为[0-1)间的一个随机数
    return min + Math.round(rand * range)
}

export const getImgSize = (url: any) => {
    if (!url || typeof url !== 'string') {
        return {} as any;
    }
    const reg = /_w(\d+)_h(\d+)/;
    const result = url.match(reg);
    if (result) {
        return {
            width: `${Number(result[1]) / 100}rem`,
            height: `${Number(result[2]) / 100}rem`,
        } as any;
    }
    return {} as any;
}

// 格式化时间
export function formatTime(times: any, type: any) {
    const time = new Date(times)
    const y = time.getFullYear()
    const m = time.getMonth() + 1
    const d = time.getDate()
    const h = time.getHours()
    const mm = time.getMinutes()
    const s = time.getSeconds()
    if (type === 'all') {
        return (
            y +
            '.' +
            add(m) +
            '.' +
            add(d) +
            ' ' +
            add(h) +
            ':' +
            add(mm) +
            ':' +
            add(s)
        )
    } else {
        return y + '.' + add(m) + '.' + add(d) + ' '
    }
}

function add(m: any) {
    return m < 10 ? '0' + m : m
}


export function conversion(money: string) {
    if(!money) return ''
    const str1: any = money.toString().match(/\d+(\.\d+)?/g);
    return str1[0]; //num2 : 2021
}


//校验手机号
export const PHONE_REG = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
// 姓名校验
export const nameRuler = /^[\u4e00-\u9fa5]{2,15}$/;
// 地址特殊表情校验
export const addressRuler = /[\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/;

//登录校验  --- 策略模式
const strategies: any = {
    checkEmpty: function (value: any) {
        return !value;
    },
    checkRule: function (value: any) {
        return !PHONE_REG.test(value);
    },
    checkNameRule: function (value: any) {
        return !nameRuler.test(value);
    },
    checkAddressRule: function (value: any) {
        return addressRuler.test(value);
    }
};
let cache: any = [];

export function Validator() {
    // 校验规则
    return {
        //添加策略事件
        add: function (value: any, method: any, message: any, skip?: any) {
            if (!skip) {
                cache.push({
                    fun: function () {
                        return strategies[method](value);
                    },
                    val: message
                });
            }
        },
        //清空策略事件
        clear: function () {
            cache = [];
        },
        // 检查
        check: function (fn: any) {
            for (let i = 0; i < cache.length; i++) {
                const valiFn = cache[i];
                const data = valiFn.fun(); // 开始检查
                if (data) {
                    fn(valiFn.val);
                    this.clear();
                    return true;
                }
            }
            return false;
        }
    };
}


/**
 * 埋点上报
 * @param name
 */
export const buriedPoint = (name: any) => {
    //@ts-ignore
    if (window._bzt?.trackEvent) {
        //@ts-ignore
        window._bzt.trackEvent('AppClick', {
            trigger_type: 'click',
            element_name: name,
        })
    }
}


export function copyCoupon(prizeCouponCode: any, fn?: any) {
    if (document.getElementById('oInput')) {
        (document.getElementById('oInput') as any).parentNode.removeChild(
            document.getElementById('oInput')
        )
    }
    const oInput = document.createElement('input')
    oInput.value = prizeCouponCode
    document.body.appendChild(oInput)
    oInput.select() // 选择对象
    document.execCommand('Copy') // 执行浏览器复制命令
    oInput.className = 'oInput'
    oInput.id = 'oInput'
    oInput.style.display = 'none'
    oInput.style.position = "absolute";
    oInput.style.left = "-1000px";
    oInput.style.zIndex = "-1000";
    oInput.setAttribute('readonly', 'readonly') //设置只读，否则移动端使用复制功能时可能会造成软件盘弹出
    oInput.blur();
    fn && fn();
}

export function linkTo(menuId: any) {
    //交费页 B10070100
    JSBridgeMethod('jumpToMenu', {data: {menuId}})
        .then((res: any) => {
            console.log('jumpToMenu 反参' + res);
            console.log('jumpToMenu 反参' + JSON.stringify(res));
        })
        .catch((res: any) => {
            console.error(res + '异常');
        });
}

const userAgent = navigator.userAgent

//判断是否是国网环境
export const isNational = userAgent.indexOf('wsgw') > -1
