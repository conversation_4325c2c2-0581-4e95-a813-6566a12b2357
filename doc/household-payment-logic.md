# 户号与缴费服务记录业务逻辑文档

## 业务背景说明

### 1. 户号管理的业务目的

在河北省钱行动派活动中，用户需要绑定国网电力户号才能参与活动。户号是用户与电力公司之间的唯一标识，用于：
- 验证用户是否为有效电力用户
- 关联用户的缴费记录
- 完成指定任务（如绑定户号任务）
- 参与锦鲤抽奖等活动

### 2. 缴费服务记录查询的业务目的

缴费记录查询用于验证用户是否在活动周期内完成了符合条件的缴费行为：
- 查询用户在指定时间范围内的缴费记录
- 验证缴费金额是否达到活动门槛（默认≥20元）
- 完成缴费相关任务，获取抽奖资格

### 3. 与河北省钱行动派活动的关联

```mermaid
graph TD
    A[用户进入活动] --> B[获取户号列表]
    B --> C{户号数量判断}
    C -->|无户号| D[提示绑定户号]
    C -->|有户号| E[查询缴费记录]
    E --> F{缴费金额判断}
    F -->|≥门槛| G[完成任务/获取抽奖码]
    F -->|<门槛| H[提示需缴费]
```

## 核心业务流程

### 1. 户号获取流程

```mermaid
graph TD
    A[isHandledHousehold] --> B{Mock模式?}
    B -->|是| C[使用mockData.powerUserList]
    B -->|否| D[调用JSBridge NLRequest]
    C --> E[handleHouseholdList处理]
    D --> F{接口响应}
    F -->|成功| E
    F -->|失败| G[错误日志]
    E --> H{户号数量}
    H -->|0个| I[openTipsDialog unBind]
    H -->|1个| J[自动选择]
    H -->|多个| K[底部选择弹框]
```

### 2. 缴费记录查询流程

```mermaid
graph TD
    A[isHandledPay] --> B{Mock模式?}
    B -->|是| C[使用mockData.serviceRecordList]
    B -->|否| D[调用JSBridge getServiceRecord]
    C --> E[handlePayList处理]
    D --> F{接口响应}
    F -->|成功| E
    F -->|失败| G[错误日志]
    E --> H{符合条件记录}
    H -->|有| I[调用任务完成接口]
    H -->|无| J[提示无符合记录]
```

## 关键方法说明

### 1. isHandledHousehold() - 户号处理入口

**功能**: 判断数据来源模式，分发到对应的处理方法

```typescript
isHandledHousehold() {
  const isMock = this.$configData?.showMockDta;
  if(isMock){
    // Mock模式：使用配置的测试数据
    const householdList = this.$configData.mockData.showPowerFlag
      ? this.$configData.mockData.powerUserList
      : [];
    this.handleHouseholdList(householdList);
    return;
  }
  // 在线模式：调用JSBridge获取真实数据
  this.getHouseholdList();
}
```

### 2. getHouseholdList() - JSBridge获取户号

**功能**: 通过国网App原生桥接获取用户绑定的电力户号列表

**接口路径**: `/osg-open-uc0001/member/c8/f68`

**请求参数**:
| 参数 | 说明 |
|------|------|
| type | 查询类型，固定为 '01' |
| accessToken | OAuth授权令牌 |
| token | 用户登录Token |
| userId | 用户ID |
| provinceId | 省份ID（河北：14000/140000） |

### 3. handleHouseholdList() - 户号数据处理

**功能**: 过滤指定省份的户号，根据数量执行不同业务分支

**过滤逻辑**:
```typescript
householdList.forEach((item: any) => {
  // 仅保留河北省/山西省的户号
  if (item.provinceId == this.isProvinceId || item.provinceId == '140000') {
    this.accountNumberList.push({
      consNo: item.powerUserNo,    // 户号
      orgNo: item.powerUnit,       // 供电单位
    });
  }
});
```

**分支处理**:
| 户号数量 | 处理方式 |
|---------|---------|
| 0个 | `openTipsDialog('unBind')` 提示绑定 |
| 1个 | 自动选择该户号 |
| 多个 | 唤起底部选择弹框 |

### 4. isHandledPay() - 缴费处理入口

**功能**: 判断数据来源，分发到对应处理方法

```typescript
isHandledPay() {
  const isMock = this.$configData.showMockDta;
  if(isMock){
    const householdList = this.$configData.mockData.showServiceFlag
      ? this.$configData.mockData.serviceRecordList
      : [];
    this.handlePayList(householdList);
    return;
  }
  this.getServiceRecord();
}
```

### 5. getServiceRecord() - 服务记录查询

**功能**: 查询用户在活动周期内的缴费服务记录

**接口路径**: `/osg-open-uc0001/member/c8/f67`

**请求参数**:
| 参数 | 说明 |
|------|------|
| busyTypeCode | 业务类型码 '02' |
| subBusyTypeCode | 子业务类型 '0201' |
| sSubBusyTypeCode | 子子业务类型 '020101' |
| startTime | 活动周期开始时间 |
| endTime | 活动周期结束时间 |
| provinceId | 省份ID |
| pageNo/pageSize | 分页参数 |

### 6. handlePayList() - 缴费记录处理

**功能**: 过滤符合条件的缴费记录，判断是否满足活动要求

**过滤规则**:
```typescript
handlePayList(payList: any[]) {
  const threshold = this.$configData?.payThreshold || 20; // 默认20元

  const validRecords = payList.filter((item: any) => {
    // 1. 省份过滤
    const isValidProvince = ['14000', '140000', '14101'].includes(item.provinceId);
    // 2. 金额过滤
    const isValidAmount = parseFloat(item.payAmount) >= threshold;
    return isValidProvince && isValidAmount;
  });

  if (validRecords.length > 0) {
    // 有符合条件的记录，完成任务
    this.completePayTask(validRecords);
  } else {
    // 无符合条件记录，提示用户
    this.$toast('暂无符合条件的缴费记录');
  }
}
```

## 数据结构说明

### 1. 户号列表数据格式 (powerUserList)

**JSBridge返回格式**:
```typescript
interface PowerUser {
  powerUserNo: string;      // 电力户号
  powerUnit: string;        // 供电单位编码
  provinceId: string;       // 省份ID
  userName: string;         // 用户姓名
  address: string;          // 用电地址
  elecTypeCode: string;     // 用电类型
}

// 响应示例
{
  code: '0',
  data: {
    powerUserList: PowerUser[]
  }
}
```

### 2. 服务记录数据格式 (serviceRecordList)

**JSBridge返回格式**:
```typescript
interface ServiceRecord {
  busyNo: string;           // 业务编号
  busyTypeName: string;     // 业务类型名称
  payAmount: string;        // 缴费金额
  payTime: string;          // 缴费时间
  provinceId: string;       // 省份ID
  consNo: string;           // 户号
  orgNo: string;            // 供电单位
}

// 响应示例
{
  code: '0',
  data: {
    serviceRecordList: ServiceRecord[]
  }
}
```

### 3. 处理后的数据结构 (accountNumberList)

```typescript
interface AccountNumber {
  consNo: string;           // 户号
  orgNo: string;            // 供电单位编码
}

// 组件内部存储
accountNumberList: AccountNumber[] = [];
```

## 业务规则

### 1. 省份ID过滤规则

| 省份ID | 说明 |
|--------|------|
| 14000 | 河北省（主要） |
| 140000 | 河北省（兼容格式） |
| 14101 | 山西省（特殊兼容） |

**过滤代码**:
```typescript
const validProvinceIds = ['14000', '140000', '14101'];
const isValidProvince = validProvinceIds.includes(item.provinceId);
```

### 2. 缴费金额阈值规则

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| payThreshold | 20 | 缴费金额门槛（元） |

**判断逻辑**:
```typescript
const threshold = this.$configData?.payThreshold || 20;
const isValid = parseFloat(record.payAmount) >= threshold;
```

### 3. 户号数量处理分支

| 数量 | 处理方式 | 用户体验 |
|------|---------|---------|
| 0 | `openTipsDialog('unBind')` | 弹窗提示绑定户号 |
| 1 | 自动选择 | 无感知，直接使用 |
| ≥2 | 底部弹框选择 | 用户手动选择户号 |

## JSBridge接口说明

### 1. 户号查询接口

**调用方式**:
```typescript
WSGWBridge.NLRequest({
  url: '/osg-open-uc0001/member/c8/f68',
  method: 'POST',
  data: {
    type: '01',
    accessToken: this.accessToken,
    token: this.token,
    userId: this.userId,
    provinceId: this.isProvinceId
  },
  success: (res) => { /* 处理响应 */ },
  fail: (err) => { /* 错误处理 */ }
});
```

**响应格式**:
```json
{
  "code": "0",
  "message": "success",
  "data": {
    "powerUserList": [
      {
        "powerUserNo": "**********",
        "powerUnit": "13101",
        "provinceId": "14000",
        "userName": "张三",
        "address": "河北省石家庄市..."
      }
    ]
  }
}
```

### 2. 服务记录查询接口

**调用方式**:
```typescript
WSGWBridge.NLRequest({
  url: '/osg-open-uc0001/member/c8/f67',
  method: 'POST',
  data: {
    busyTypeCode: '02',
    subBusyTypeCode: '0201',
    sSubBusyTypeCode: '020101',
    startTime: this.activityStartTime,
    endTime: this.activityEndTime,
    provinceId: this.isProvinceId,
    pageNo: 1,
    pageSize: 100,
    accessToken: this.accessToken,
    token: this.token
  },
  success: (res) => { /* 处理响应 */ },
  fail: (err) => { /* 错误处理 */ }
});
```

### 3. Token说明

| Token类型 | 来源 | 用途 |
|-----------|------|------|
| accessToken | OAuth授权流程 | 国网开放平台API鉴权 |
| token | 用户登录 | 业务接口身份验证 |
| sgccToken | getAuthToken接口 | 活动后端接口鉴权 |

## Mock数据模式

### 1. 开启Mock模式

在配置文件中设置：
```typescript
// config.json
{
  "showMockDta": true,  // 开启Mock模式
  "mockData": {
    "showPowerFlag": true,   // 是否返回户号数据
    "showServiceFlag": true, // 是否返回服务记录
    "powerUserList": [...],  // 模拟户号列表
    "serviceRecordList": [...] // 模拟服务记录
  }
}
```

### 2. Mock配置项说明

| 配置项 | 类型 | 说明 |
|--------|------|------|
| showMockDta | boolean | 总开关，true启用Mock |
| showPowerFlag | boolean | 户号Mock开关 |
| showServiceFlag | boolean | 服务记录Mock开关 |
| powerUserList | array | 模拟户号数据 |
| serviceRecordList | array | 模拟服务记录数据 |

### 3. Mock数据示例

```typescript
// 户号Mock数据
powerUserList: [
  {
    powerUserNo: '**********',
    powerUnit: '13101',
    provinceId: '14000',
    userName: '测试用户',
    address: '河北省石家庄市测试地址'
  }
]

// 服务记录Mock数据
serviceRecordList: [
  {
    busyNo: 'BZ202412010001',
    busyTypeName: '电费缴纳',
    payAmount: '50.00',
    payTime: '2024-12-01 10:30:00',
    provinceId: '14000',
    consNo: '**********',
    orgNo: '13101'
  }
]
```

## 异常处理和用户提示

### 1. 户号为空处理

```typescript
if (this.accountNumberList.length === 0) {
  // 弹出绑定户号提示弹窗
  this.openTipsDialog('unBind');
}
```

**弹窗内容**:
- 标题：请先绑定户号
- 描述：参与活动需要绑定电力户号
- 按钮：去绑定（跳转绑定页面）

### 2. 无符合条件缴费记录

```typescript
if (validRecords.length === 0) {
  this.$toast('暂无符合条件的缴费记录');
  // 或显示引导缴费弹窗
  this.openTipsDialog('noPay');
}
```

### 3. JSBridge调用失败

```typescript
WSGWBridge.NLRequest({
  // ...
  fail: (err) => {
    console.error('JSBridge调用失败:', err);
    myTrackEvent('JSBridgeError', {
      api: '/osg-open-uc0001/member/c8/f68',
      error: JSON.stringify(err)
    });
    this.$toast('网络异常，请稍后重试');
  }
});
```

### 4. 错误码处理

| 错误码 | 说明 | 处理方式 |
|--------|------|---------|
| 0 | 成功 | 正常处理数据 |
| 401 | Token失效 | 重新获取Token |
| 500 | 服务器错误 | 提示稍后重试 |
| -1 | 网络异常 | 检查网络连接 |

## 流程图总览

```mermaid
sequenceDiagram
    participant U as 用户
    participant H as Home页面
    participant B as JSBridge
    participant S as 后端服务

    U->>H: 进入活动页面
    H->>H: isHandledHousehold()
    alt Mock模式
        H->>H: 使用mockData
    else 在线模式
        H->>B: NLRequest(户号查询)
        B->>S: 请求户号列表
        S-->>B: 返回户号数据
        B-->>H: 回调响应
    end
    H->>H: handleHouseholdList()
    alt 无户号
        H->>U: 提示绑定户号
    else 有户号
        H->>H: isHandledPay()
        alt Mock模式
            H->>H: 使用mockData
        else 在线模式
            H->>B: NLRequest(服务记录)
            B->>S: 请求缴费记录
            S-->>B: 返回服务记录
            B-->>H: 回调响应
        end
        H->>H: handlePayList()
        alt 有符合条件记录
            H->>S: 完成任务接口
            H->>U: 显示成功提示
        else 无符合条件记录
            H->>U: 提示需要缴费
        end
    end
```

---

**文档版本**: 1.0
**更新日期**: 2024-12
**适用项目**: 河北省钱行动派

