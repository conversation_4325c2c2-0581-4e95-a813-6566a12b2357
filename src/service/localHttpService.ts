import { Http } from "@bangdao/apollo";
import { IHttpRequest } from "@bangdao/apollo/es/type";
import { globalConfig } from "@/utils/config";
import EventBus from "@/utils/evnetBus";
import { myTrackEvent, GATEWAY_TYPE } from "@/common/xmp";
import { Toast } from "vant";
import store from "@/store";
import { doDecrypt, doEncrypt } from "@/utils/sm2";
const sm3 = require("sm-crypto").sm3;

export interface LocalHttpServiceDeps {
  configData: {
    isShowCheckTime?: boolean;
    code?: any;
    otherCode?: any;
  };
  microUtils: {
    getTimeStamp: () => number | undefined;
    getToken: (key: string) => string | undefined;
  };
}

export default class LocalHttpService {
  private http: Http;
  private configData: LocalHttpServiceDeps["configData"];
  private microUtils: LocalHttpServiceDeps["microUtils"];

  constructor(deps: LocalHttpServiceDeps) {
    this.http = new Http(globalConfig.httpConfig);
    this.configData = deps.configData;
    this.microUtils = deps.microUtils;
  }

  // 本地化接口拦截器
  public localPost(options: IHttpRequest): Promise<any> {
    // 显示loading
    if ((options as any).loading !== false) {
      Toast.loading({
        message: (options as any).loadingText || "加载中...",
        forbidClick: true,
        duration: 0,
      });
    }

    console.log('🚀 ~ file:localHttpService method:localPost line:45 -----', );

    const isShowCheckTime = !!this.configData.isShowCheckTime;
    if (this.microUtils.getTimeStamp && this.microUtils.getTimeStamp() && isShowCheckTime) {
      const oldTime: number = this.microUtils.getTimeStamp() as number,
        curTime: number = new Date().getTime();
      // 30分钟
      if (Math.abs(curTime - oldTime) > 29 * 60 * 1000) {
        Toast.clear();
        EventBus.$emit("showBounced");
        //@ts-ignore 与原实现保持一致
        return [undefined, undefined] as any;
      }
    }

    const { data, url } = options as any;
    const newOptions = { data: { ...data } };
    const { ...res } = newOptions.data;

    if (!(res as any).sgccToken) {
      (res as any).token =
        (this.microUtils.getToken && this.microUtils.getToken("Token")) ||
        (store.state as any).isTokenData.token ||
        (globalConfig.httpConfig as any).token;
      (res as any).sgccToken =
        (this.microUtils.getToken && this.microUtils.getToken("authToken")) ||
        (store.state as any).isTokenData.sgccToken;
    }

    // 仅日志与原实现保持一致
    // eslint-disable-next-line no-console
    console.log("接口:", url);
    // eslint-disable-next-line no-console
    console.log("🚀 ~ file:localHttpService.ts method:localPost -----", { ...res });

    const body = doEncrypt(JSON.stringify(data));
    const sign = sm3(JSON.stringify(data));

    const paramsData = {
      url: `${process.env.VUE_APP_BASE_LOCAL_URL + url}`,
      data: {
        body,
        sign,
      },
    };

    const extendsOptions = {
      withCredentials: true,
      timeout: 15000,
    } as const;

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error("Local request timeout"));
      }, 15000);
    });

    const httpPromise = this.http.post({
      ...paramsData,
      contentType: "application/json",
      extendsOptions: extendsOptions as any,
      header: { authToken: (res as any).sgccToken, source: "SGCC" },
    });

    return this.localPromiseWapper(
      Promise.race([httpPromise, timeoutPromise]),
      { type: "post", params: { ...paramsData } }
    );
  }

  private localPromiseWapper(
    $promise: any,
    { type, params }: any
  ): Promise<[any, any]> {
    try {
      // eslint-disable-next-line no-console
      console.log("接口请求：", type, params);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error, "getStorage error");
    }
    return new Promise((resolve, reject) => {
      $promise
        .then(async (res: any) => {
          Toast.clear();
          const { data: resData = {} } = res || {};
          const { content: originContent = {} } = resData as any;
          let response;
          if ((originContent as any)?.encryData) {
            response = await doDecrypt((originContent as any).encryData);
          } else {
            response = originContent;
          }
          // 仅日志，与原实现保持一致
          // eslint-disable-next-line no-console
          console.info(
            "%c get login code success ?",
            "color: #68C174; font-weight: bold",
            response
          );
          if (res && (res as any).code) {
            // eslint-disable-next-line no-console
            console.log(res, "首次res");
            if ((res as any).code === 200) {
              resolve([(res as any).data || {}, undefined]);
            } else {
              // 业务异常（含登录失效场景）
              if (
                (res as any).code == (this.configData as any).code ||
                (res as any).code == (this.configData as any).otherCode
              ) {
                EventBus.$emit("showBounced");
                //@ts-ignore 与原实现保持一致
                return [undefined, undefined] as any;
              }
              resolve([undefined, res]);
            }
          } else {
            myTrackEvent("BizError", {
              type: GATEWAY_TYPE,
              method: (params as any).method,
              code: (res as any).code,
              message: (res as any).message ? (res as any).message : "接口异常",
              biz_content: GATEWAY_TYPE,
            });
            resolve([
              undefined,
              {
                message: (res as any).message || "不好意思，出错啦\n请稍后再来～～",
              },
            ]);
          }
        })
        .catch((err: any) => {
          myTrackEvent("BizError", {
            type: GATEWAY_TYPE,
            method: (params as any).method,
            message: err || "接口异常",
            biz_content: GATEWAY_TYPE,
          });
          // eslint-disable-next-line no-console
          console.info(
            "%c get login scode failed ?",
            "color: #DC143C; font-weight: bold",
            err
          );
          resolve([undefined, err]);
          reject();
        })
        .finally(() => {
          setTimeout(() => {
            Toast.clear();
          }, 1000);
        });
    });
  }
}

