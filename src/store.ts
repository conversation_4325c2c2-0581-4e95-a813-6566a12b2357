import Vue from 'vue';
import Vuex from 'vuex';
import { IMAGE } from '@/utils/constant';

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    IMAGE: IMAGE, // 图片集合
    location: {
      lng: '',
      lat: ''
    },
    isTokenData: {
      token: '',
      sgccToken: ''
    },
  },
  mutations: {
    SET_LOCATION: (state: any, payload: any) => {
      state.location = payload;
    },
    setTokenData: (state: any, payload: any) => {
      state.isTokenData = payload;
    },
  },
  actions: {},
});
