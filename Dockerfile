# FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:11.6-alpine as build

FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:12.18.0-bd as build
WORKDIR /tmp
COPY . .
# 设置私有源
#RUN npm config set registry https://registry.npm.taobao.org
RUN npm cache verify
RUN npm cache clean -f
# 安装指定版本
RUN npm install --registry https://registry.npm.taobao.org
RUN npm run build:dev
## run with express
## EXPOSE 3000
## CMD ["npm", "run", "express:run"]
FROM registry.cn-hangzhou.aliyuncs.com/leo_library/nginx:1.12.2
WORKDIR /usr/share/nginx/html/app/hebei-money-saving-act/
RUN rm -f *
COPY --from=build /tmp/dist .
COPY --from=build /tmp/default.conf /etc/nginx/conf.d/default.conf
