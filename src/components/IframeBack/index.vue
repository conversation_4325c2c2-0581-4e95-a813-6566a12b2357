<template>
  <div
    class="goBack"
    :style="{
      position: `${isCover ? 'absolute' : ''}`,
      backgroundColor: `${isCover ? '' : bgColor}`
    }"
  >
    <div
      class="backImg"
      :style="{
        backgroundImage: `url(${backIcon ||
          'https://psm.bangdao-tech.com/interaction-putting/20316/img/20230525162050719083035111001366_w54_h54.png'})`,
        width: `0.54rem`
      }"
      @click="goBack"
    ></div>
    <div class="back-tit">{{ title }}</div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class BackButton extends Vue {
  @Prop({
    type: String,
    default: ''
  })
  backIcon!: string; // 默认为 “透明返回按钮”

  @Prop({
    type: Boolean,
    default: true
  })
  isCover!: boolean; // 默认为 “覆盖在页面元素”

  @Prop({
    type: String,
    default: '#ffffff'
  })
  bgColor;

  @Prop({
    type: String,
    default: ''
  })
  title; // 标题

  @Prop({
    type: String,
    default: ''
  })
  pathName: string | undefined; // 指定返回页面name

  @Prop({
    type: Boolean,
    default: false
  })
  isClick!: boolean;

  goBack() {
    if (this.pathName) {
      this.$router.replace({
        name: this.pathName
      });
    } else if (this.isClick) {
      this.$emit('click');
    } else {
      this.$router.go(-1);
    }
  }
}
</script>
<style lang="less" scoped>
// 顶部返回键
.goBack {
  width: 100%;
  padding-top: 0.3rem; //0.27rem;
  background-color: transparent;
  z-index: 999;
  display: flex;
  align-items: center;
  // position: absolute;

  .backImg {
    z-index: 2;
    width: 0.54rem; //0.25rem;
    height: 0.54rem; //0.25rem;
    margin-left: 0.3rem; //0.15rem;
    background-repeat: no-repeat;
    background-size: contain;
  }

  .back-tit {
    margin-left: 0.1rem; //0.05rem;
    color: #fff;
  }
}
</style>
