import Vue from 'vue';
import Router from 'vue-router';

const originalPush: any = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err: any) => err);
};

const originalReplace: any = Router.prototype.replace;
Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err: any) => err);
};

Vue.use(Router);

export default new Router({
  base: '',
  mode: 'hash',
  routes: [
    {
      path: '/home',
      name: 'home',
      component: () => import(/*webpackChunkName: "home"*/ './pages/home/<USER>'),
    },
    {
      path: "/link",
      name: "link",
      component: () =>
        import(/*webpackChunkName: "banner"*/ "./pages/banner/link.vue")
    },
    {
      path: '/rule',
      name: 'rule',
      component: () => import(/*webpackChunkName: "home"*/ './pages/home/<USER>'),
    },
  ],
});
