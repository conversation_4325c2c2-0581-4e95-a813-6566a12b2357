# 网络接口封装使用说明文档

## 概述

项目采用分层架构设计，通过 `BaseService` 基类、`RemoteApiService` 远程服务类和 `LocalHttpService` 本地服务类实现网络请求的统一封装和管理。基于邦道Apollo HTTP库进行二次封装，支持SM2/SM3国密加解密、错误处理、Loading状态管理、请求重试、缓存等功能。

## 架构设计

### 核心类结构

```
BaseService (基础服务类)
    ├── RemoteApiService (远程网关API服务)
    └── LocalHttpService (本地化接口服务)
         ↓
Api (业务API类) extends BaseService
         ↓
userService (服务实例导出)
```

### 服务层文件结构

```
src/service/
├── baseService.ts      # 基础服务类，提供统一的请求入口
├── remoteApiService.ts # 远程网关API服务，支持加解密、重试、缓存
├── localHttpService.ts # 本地化接口服务
├── api.ts              # 业务API定义
└── service.d.ts        # 类型定义
```

## BaseService 基础服务类

### 核心功能

1. **请求封装**: 统一的POST/GET请求方法
2. **加解密处理**: 支持SM2/SM3国密加解密
3. **错误处理**: 统一的错误处理和响应格式化
4. **Loading管理**: 自动显示和隐藏加载状态
5. **时间校验**: 支持token时效性检查
6. **请求重试**: 支持超时重试机制
7. **请求缓存**: 支持响应数据缓存

### 主要方法

#### post() 方法 - 远程网关请求
```typescript
protected post(options: IHttpRequest, config?: {
  retryCount?: number;      // 重试次数，默认0
  retryDelay?: number;      // 重试间隔(ms)，默认600
  cache?: boolean;          // 是否启用缓存，默认false
  cacheTTL?: number;        // 缓存有效期(ms)，默认30000
  showLoading?: boolean;    // 是否显示Loading，默认true
  loadingText?: string;     // Loading文案，默认"加载中..."
  signal?: AbortSignal;     // 取消请求信号
  headers?: Record<string, string>; // 额外请求头
  timeout?: number;         // 超时时间(ms)，默认30000
  enableTimeoutRetry?: boolean; // 是否启用超时重试，默认true
}): Promise<any>
```

**请求参数结构**:
```typescript
{
  data: {
    method: string,           // 接口方法编号，如 '10001'
    ...customParams           // 业务自定义参数
  }
}
```

#### localPost() 方法 - 本地化接口请求
```typescript
protected localPost(options: IHttpRequest): Promise<any>
```

#### 配置方法
```typescript
// 配置远程API请求默认参数
protected configRemoteApi(config: RemoteApiRuntimeConfig): void

// 清空远程API请求缓存
protected clearRemoteCache(): void

// 清理所有loading状态
protected clearAllLoading(): void

// 取消所有活跃请求
protected cancelAllRequests(): void
```

**返回值格式**: `[data, error]`
- 成功: `[rtnData, undefined]`
- 失败: `[undefined, errorData]`

**响应处理逻辑**:
```typescript
// 成功条件 (rtnFlag === 9999)
if (rtnFlag === 9999) {
  resolve([rtnData, undefined]);
} else {
  resolve([undefined, { rtn_flag: rtnFlag, rtn_msg: rtnMsg }]);
}
```

## RemoteApiService 远程API服务类

### 核心特性

1. **SM2/SM3加解密**: 请求参数使用SM2加密，响应数据SM2解密
2. **请求重试**: 支持超时自动重试
3. **响应缓存**: 支持接口响应缓存
4. **Loading管理**: 统一的加载状态管理
5. **请求取消**: 支持AbortController取消请求
6. **埋点上报**: 自动上报接口异常

### 加解密流程

```
请求流程:
原始数据 → JSON.stringify → SM3签名 → SM2加密 → 发送请求

响应流程:
加密响应 → SM2解密 → Base64解码 → JSON解析 → 返回数据
```

## Api 业务服务类

**文件路径**: `src/service/api.ts`

### 接口列表

#### 1. 获取国网SGCC授权Token
```typescript
public getAuthToken(options: any): Promise<[any, any]>
```
- **接口方法**: `10001`
- **用途**: 获取国网sgcc授权token

#### 2. 活动用户上报
```typescript
public reportUser(options: any): Promise<[any, any]>
```
- **接口方法**: `10006`
- **用途**: 活动参与人数统计埋点

#### 3. 获取任务列表
```typescript
public getTaskList(options: any): Promise<[any, any]>
```
- **接口方法**: `10003`
- **用途**: 获取通用任务列表

#### 4. 完成任务
```typescript
public completeTask(options: any): Promise<[any, any]>
```
- **接口方法**: `10004`
- **用途**: 标记任务完成

#### 5. 获取活动列表
```typescript
public getActivityList(options: any): Promise<[any, any]>
```
- **接口方法**: `10002`
- **用途**: 获取锦鲤活动列表

#### 6. 获取活动轮次信息
```typescript
public getActivityRound(options: any): Promise<[any, any]>
```
- **接口方法**: `10019`
- **用途**: 获取活动轮次信息

#### 7. 获取抽奖码信息
```typescript
public getActivityCode(options: any): Promise<[any, any]>
```
- **接口方法**: `10005`
- **用途**: 获取每个轮次的抽奖码信息

## 使用示例

### 基本用法

```typescript
import { userService } from '@/service/api';

// 在Vue组件中使用
export default class HomeComponent extends Vue {
  async getActivityData() {
    const [res, err] = await userService.getActivityList({});

    if (err) {
      // 错误处理
      this.$toast(err.rtn_msg || '请求失败');
      return;
    }

    if (res) {
      // 成功处理
      console.log('活动数据:', res);
      this.activityData = res;
    }
  }
}
```

### 带配置的请求

```typescript
async fetchWithConfig() {
  const [res, err] = await userService.getTaskList({
    // 业务参数
  }, {
    retryCount: 2,        // 失败重试2次
    cache: true,          // 启用缓存
    cacheTTL: 60000,      // 缓存1分钟
    showLoading: true,    // 显示loading
    timeout: 15000        // 15秒超时
  });
}
```

### 错误处理最佳实践

```typescript
async handleApiCall() {
  const [res, err] = await userService.getActivityList({});

  if (err) {
    // 检查是否为超时错误
    if (err.isTimeout) {
      this.$toast('请求超时，请检查网络连接');
      return;
    }

    // 根据错误码进行不同处理
    const { rtn_flag, rtn_msg } = err;

    // Token失效场景由EventBus统一处理
    // 其他业务错误
    this.$toast(rtn_msg || '系统繁忙，请稍后再试');
    return;
  }

  // 处理成功响应
  this.handleSuccess(res);
}
```

### 取消请求

```typescript
export default class MyComponent extends Vue {
  private abortController?: AbortController;

  async fetchData() {
    // 取消之前的请求
    this.abortController?.abort();
    this.abortController = new AbortController();

    const [res, err] = await userService.getActivityList({}, {
      signal: this.abortController.signal
    });
  }

  beforeDestroy() {
    // 组件销毁时取消请求
    this.abortController?.abort();
  }
}
```

## 配置说明

### 全局配置 (globalConfig)

**文件路径**: `src/utils/config.ts`

```typescript
export const globalConfig = {
  httpConfig: {
    appId: '2015121300967512',
    getWayURL: getGatewayUrl(),  // 根据环境自动获取网关地址
    platform: 'h5',
    token: '99tt2aec4d17540748d5a45f30cda92fd8c6',
    userId: '97ua0568bd3019fc4bebb4c7661fde01'
  },
  standardHttpConfig: {
    rtnCode: [200],           // 网关成功码
    bizCode: ['9999'],        // 业务成功码
    bizKeys: ['content', 'rtn_flag'],  // 业务响应路径
    rtnKes: ['ret_code'],     // 网关响应路径
    loopCode: [5006, 5007]    // 需要重新获取token的错误码
  }
};
```

### 网关地址配置

```typescript
export function getGatewayUrl() {
  const HOST_NAME = window.location.hostname;
  switch (HOST_NAME) {
    case 'gateway.bangdao-tech.com':
      return 'http://gateway.bangdao-tech.com:30001/gateway.do';
    case 'release-ali.bangdao-tech.com':
    case 'testali.bangdao-tech.com':
      return 'https://release-openapi.bangdao-tech.com/gateway.do';
    case 'ali.bangdao-tech.com':
    case 'm.bangdao-tech.com':
      return 'https://oapi.bangdao-tech.com/gateway.do';
    default:
      return process.env.VUE_APP_BASE_LOCAL_URL;
  }
}
```

### 运行时配置

通过 `this.$configData` 获取运行时配置:
- `isShowEncryption`: 是否启用SM2/SM3加解密
- `isShowCheckTime`: 是否启用时间校验
- `code` / `otherCode`: Token失效错误码

## 安全特性

### 1. SM2/SM3国密加解密

```typescript
// src/utils/sm2.ts
// SM2加密
export const doEncrypt = (value: any) => {
  const data = '04' + sm2.doEncrypt(value, publicKey, cipherMode);
  return data;
}

// SM2解密
export const doDecrypt = (value: any) => {
  const data = sm2.doDecrypt(value.substr(2), privateKey, cipherMode);
  return JSON.parse(jsonrepair(Base64.decode(data)));
}

// SM3签名
const sign = sm3(JSON.stringify(data));
```

### 2. 请求头安全配置

```typescript
const baseHeaders = {
  "Content-Type": "application/json;charset=utf-8",
  "X-Timestamp": new Date().getTime(),  // 时间戳
  "X-Authorization": sgccToken           // 国网授权Token
};
```

### 3. 时间戳校验

```typescript
// 30分钟时效检查
if (Math.abs(curTime - oldTime) > 29 * 60 * 1000) {
  EventBus.$emit("showBounced");  // 触发重新登录
  return [undefined, undefined];
}
```

### 4. 错误追踪埋点

```typescript
myTrackEvent("BizError", {
  type: GATEWAY_TYPE,
  method: params.data?.method,
  code: rtnFlag,
  message: rtnMsg || "接口异常",
  biz_content: GATEWAY_TYPE
});
```

## 注意事项

1. 所有接口调用默认显示Loading，可通过 `showLoading: false` 关闭
2. 接口返回采用 `[data, error]` 格式，需要先判断error再处理data
3. Token失效时会通过EventBus触发 `showBounced` 事件
4. 加解密功能基于 `isShowEncryption` 配置开关
5. 超时请求会自动清理Loading状态并支持重试
6. 支持请求缓存，避免重复请求相同数据
