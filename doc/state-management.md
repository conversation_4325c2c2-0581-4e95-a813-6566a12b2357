# 数据流（状态管理）使用说明文档

## 概述

项目采用Vuex进行集中式状态管理，结合vuex-class装饰器实现类组件中的状态绑定。同时使用EventBus进行组件间事件通信。状态管理主要用于存储全局共享数据，如图片资源、用户位置信息、Token数据等。

## Vuex Store 结构

### Store 配置

**文件路径**: `src/store.ts`

```typescript
import Vue from 'vue';
import Vuex from 'vuex';
import { IMAGE } from '@/utils/constant';

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    IMAGE: IMAGE,        // 图片集合
    location: {          // 用户位置信息
      lng: '',           // 经度
      lat: ''            // 纬度
    },
    isTokenData: {       // Token数据
      token: '',         // 用户Token
      sgccToken: ''      // 国网授权Token
    }
  },
  mutations: {
    SET_LOCATION: (state: any, payload: any) => {
      state.location = payload;
    },
    setTokenData: (state: any, payload: any) => {
      state.isTokenData = payload;
    }
  },
  actions: {},
});
```

## State 状态定义

### 1. IMAGE 图片资源

**类型**: `Object`
**用途**: 存储全局使用的图片资源URL

**数据结构**:
```typescript
interface ImageState {
  CLOSE: string;  // 关闭按钮图片
  BACK: string;   // 返回按钮图片
}
```

### 2. location 位置信息

**类型**: `Object`
**用途**: 存储用户当前地理位置信息

**数据结构**:
```typescript
interface LocationState {
  lng: string;  // 经度
  lat: string;  // 纬度
}
```

### 3. isTokenData Token数据

**类型**: `Object`
**用途**: 存储用户认证Token信息

**数据结构**:
```typescript
interface TokenDataState {
  token: string;      // 用户Token
  sgccToken: string;  // 国网SGCC授权Token
}
```

## Mutations 状态变更

### SET_LOCATION

**功能**: 更新用户位置信息
**参数**:
- `state`: 当前状态
- `payload`: 位置信息对象 `{ lng: string, lat: string }`

**使用示例**:
```typescript
// 在组件中提交mutation
this.$store.commit('SET_LOCATION', {
  lng: '116.401768',
  lat: '39.918884'
});
```

### setTokenData

**功能**: 更新Token数据
**参数**:
- `state`: 当前状态
- `payload`: Token数据对象 `{ token: string, sgccToken: string }`

**使用示例**:
```typescript
// 在组件中提交mutation
this.$store.commit('setTokenData', {
  token: 'user_token_xxx',
  sgccToken: 'sgcc_auth_token_xxx'
});
```

## 在组件中使用状态管理

### 1. 使用 vuex-class 装饰器

```typescript
import { Vue, Component } from 'vue-property-decorator';
import { State, Mutation } from 'vuex-class';

@Component
export default class MyComponent extends Vue {
  // 绑定state
  @State('IMAGE') IMAGE!: any;
  @State('location') location!: any;
  
  // 绑定mutation
  @Mutation('SET_LOCATION') setLocation!: (payload: any) => void;
  
  mounted() {
    // 使用state
    console.log('关闭按钮图片:', this.IMAGE.CLOSE);
    console.log('当前位置:', this.location);
    
    // 调用mutation
    this.setLocation({
      lng: '116.401768',
      lat: '39.918884'
    });
  }
}
```

### 2. 直接使用 $store

```typescript
export default class MyComponent extends Vue {
  mounted() {
    // 获取state
    const images = this.$store.state.IMAGE;
    const location = this.$store.state.location;
    
    // 提交mutation
    this.$store.commit('SET_LOCATION', {
      lng: '116.401768',
      lat: '39.918884'
    });
  }
}
```

### 3. 在模板中使用

```vue
<template>
  <div>
    <!-- 使用图片资源 -->
    <img :src="$store.state.IMAGE.CLOSE" alt="关闭" />
    
    <!-- 显示位置信息 -->
    <div>
      位置: {{ $store.state.location.lng }}, {{ $store.state.location.lat }}
    </div>
  </div>
</template>
```

## 实际应用场景

### 1. 位置信息管理

在首页组件中获取和存储用户位置：

```typescript
// src/pages/home/<USER>
import { State, Mutation } from 'vuex-class';

@Component
export default class Home extends Vue {
  @State('location') storeLocation!: any;
  @Mutation('SET_LOCATION') setLocation!: (payload: any) => void;
  
  async getLocation() {
    // 获取用户位置
    const position = await this.getCurrentPosition();
    
    // 存储到store
    this.setLocation({
      lng: position.lng,
      lat: position.lat
    });
    
    // 调用接口时使用store中的位置
    this.getActivityInfo(this.storeLocation);
  }
  
  async getActivityInfo(position: any) {
    const [res, err] = await userService.homeQuery({
      activity_id: this.homeConfig.activity_id,
      extend_params: `${position.lng},${position.lat}`
    });
  }
}
```

### 2. 图片资源统一管理

在Modal组件中使用全局图片资源：

```typescript
// src/components/modal/Modal.vue
import { State } from 'vuex-class';

@Component
export default class Modal extends Vue {
  @State('IMAGE') IMAGE!: any;
  
  // 在模板中使用
  // <div :style="{ backgroundImage: `url(${IMAGE.CLOSE})` }"></div>
}
```

## 扩展状态管理

### 添加新的State

1. **在store.ts中添加state**:
```typescript
export default new Vuex.Store({
  state: {
    IMAGE: IMAGE,
    location: { lng: '', lat: '' },
    // 新增状态
    userInfo: {
      mobile: '',
      nickname: '',
      isLogin: false
    }
  },
  // ...
});
```

2. **添加对应的mutations**:
```typescript
mutations: {
  SET_LOCATION: (state: any, payload: any) => {
    state.location = payload;
  },
  // 新增mutation
  SET_USER_INFO: (state: any, payload: any) => {
    state.userInfo = { ...state.userInfo, ...payload };
  },
  SET_LOGIN_STATUS: (state: any, status: boolean) => {
    state.userInfo.isLogin = status;
  }
}
```

3. **在组件中使用**:
```typescript
@Component
export default class MyComponent extends Vue {
  @State('userInfo') userInfo!: any;
  @Mutation('SET_USER_INFO') setUserInfo!: (payload: any) => void;
  @Mutation('SET_LOGIN_STATUS') setLoginStatus!: (status: boolean) => void;
  
  login() {
    this.setUserInfo({
      mobile: '138****8888',
      nickname: '用户昵称'
    });
    this.setLoginStatus(true);
  }
}
```

### 添加Actions（异步操作）

```typescript
actions: {
  // 异步获取用户位置
  async fetchLocation({ commit }) {
    try {
      const position = await getCurrentPosition();
      commit('SET_LOCATION', {
        lng: position.lng,
        lat: position.lat
      });
      return position;
    } catch (error) {
      console.error('获取位置失败:', error);
      throw error;
    }
  },
  
  // 异步获取用户信息
  async fetchUserInfo({ commit }, token: string) {
    try {
      const [res, err] = await userService.infoQuery({ token });
      if (res) {
        commit('SET_USER_INFO', res.user_info);
        return res;
      }
      throw new Error(err?.rtn_msg || '获取用户信息失败');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }
}
```

在组件中使用Actions：

```typescript
import { Action } from 'vuex-class';

@Component
export default class MyComponent extends Vue {
  @Action('fetchLocation') fetchLocation!: () => Promise<any>;
  @Action('fetchUserInfo') fetchUserInfo!: (token: string) => Promise<any>;
  
  async mounted() {
    try {
      // 获取位置信息
      await this.fetchLocation();
      
      // 获取用户信息
      const token = this.$microUtils.getToken('Token');
      await this.fetchUserInfo(token);
    } catch (error) {
      this.$toast('初始化失败');
    }
  }
}
```

## 最佳实践

### 1. 状态设计原则
- **单一数据源**: 所有状态集中在store中管理
- **状态最小化**: 只存储必要的全局共享状态
- **不可变性**: 通过mutations修改状态，保证数据流向清晰

### 2. 命名规范
- **State**: 使用camelCase，如`userInfo`、`location`
- **Mutations**: 使用SCREAMING_SNAKE_CASE，如`SET_USER_INFO`
- **Actions**: 使用camelCase，如`fetchUserInfo`

### 3. 类型安全
```typescript
// 定义State类型
interface RootState {
  IMAGE: ImageState;
  location: LocationState;
  userInfo: UserInfoState;
}

// 在组件中使用类型
@State('userInfo') userInfo!: UserInfoState;
```

### 4. 模块化管理
对于大型应用，建议将store拆分为模块：

```typescript
// store/modules/user.ts
export default {
  namespaced: true,
  state: { /* user state */ },
  mutations: { /* user mutations */ },
  actions: { /* user actions */ }
};

// store/index.ts
import user from './modules/user';

export default new Vuex.Store({
  modules: {
    user
  }
});
```

## EventBus 事件总线

除了Vuex状态管理，项目还使用EventBus进行组件间事件通信。

### EventBus 配置

**文件路径**: `src/utils/evnetBus.ts`

```typescript
import Vue from "vue";
const EventBus = new Vue();
export default EventBus;
```

### 常用事件

| 事件名 | 触发场景 | 用途 |
|--------|----------|------|
| `showBounced` | Token失效/时间校验失败 | 显示重新登录弹窗 |
| `clearAllLoading` | 请求超时 | 清理所有Loading状态 |
| `tokenCallback` | Token获取成功 | 通知组件Token已就绪 |

### 使用示例

```typescript
import EventBus from '@/utils/evnetBus';

// 监听事件
EventBus.$on('showBounced', () => {
  // 显示重新登录弹窗
  this.showLoginDialog();
});

// 触发事件
EventBus.$emit('showBounced');

// 移除监听
EventBus.$off('showBounced');
```

### MicroUtils 自定义事件总线

`MicroUtils` 类还提供了自定义事件总线用于微应用通信：

```typescript
// src/common/microUtils.ts
public customEventBus = {
  $add: (name: string, fun: string) => { /* 添加监听 */ },
  $emit: (name: string, ...args: any) => { /* 触发事件 */ },
  $off: (name: string, callback?: any) => { /* 移除监听 */ }
};

// 使用示例
this.$microUtils.customEventBus.$add('tokenCallback', () => {
  console.log('Token获取成功');
});

this.$microUtils.customEventBus.$emit('tokenCallback');
```

## 调试工具

推荐使用Vue DevTools进行状态调试：
1. 查看当前状态树
2. 追踪mutations历史
3. 时间旅行调试
4. 导出/导入状态快照
