import { getImgSize } from '@/utils/common';

/**
 * 判断参数是否为空
 * @param  {[String]}  value 待验证的参数
 * @return {Boolean}   ture-空，false-不为空
 */
export function isEmpty(value: any) {
  if (typeof value === "undefined" || value === null || value === "") {
    return true;
  } else {
    return false;
  }
}

/**
 * 防抖函数
 */
let timeout: any;

export function debounce(fn: any, wait = 500) {
  if (timeout !== null) {
    clearTimeout(timeout);
  }
  timeout = setTimeout(fn, wait);
}

const ua = navigator.userAgent;

// 支付宝环境
export function isAlipay() {
  return /AlipayClient/i.test(ua);
}

// 判断是支付宝还是国网
export function getAppSource() {
  if (isAlipay()) {
    return false;
  } else {
    return true;
  }
}

//ios 环境
export function isiOS() {
  return !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
}

export function objForEach(obj: object, callback: Function) {
  // 判断回调是否是一个函数
  if (typeof callback === "function") {
    let i = 0;
    for (const key in obj) {
      // @ts-ignore
      callback(obj[key], i, key);
      i++;
    }
    return;
  }
  // 传入的回调如果不是function，那么就抛出错误
  throw new Error(
    callback +
      " is not a function!,You can use it like this: Object.forEach(obj,(item,index,key)=>{...}) "
  );
}

export function getImage(imgUrl?: string) {
  const base: any = {
    backgroundSize: '100% 100%',
    backgroundRepeat: 'no-repeat',
  };
  if (imgUrl) {
    base.backgroundImage = `url(${imgUrl})`;
    Object.assign(base, getImgSize(imgUrl));
  }
  return base;
}


/**
 * list转map
 * @param {[]} list
 * @param {String} keyName 转map作为key的字段名
 * @returns
 */
export function list2Map(list: any[], keyName: string) {
  const map :any = {}
  let item
  for (let i = 0, len = list.length; i < len; i++) {
    item = list[i]
    map[item[keyName]] = item
  }
  return map
}
