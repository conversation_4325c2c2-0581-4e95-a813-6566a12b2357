<template>
  <div>
    <background v-bind="bgLayout">
      <InitMask v-if="!isReady"></InitMask>
      <backButton :is-close="true"></backButton>
      <div class="hot-area">
        <div
          class="hot-area-list"
          v-for="(item, index) in dataList"
          :key="index"
          :style="hotAreaList(item)"
          @click="itemClickHandle(item)"
        ></div>
      </div>
<!--   翻倍包icon   -->
      <div class="bag-icon" :style="getImage(homeConfig.imgList.bagIcon)">
        <div class="bag-title font_22">翻倍包:<span>{{bagNum}}</span></div>
      </div>
<!--  倒计时    -->
      <div class="time-icon" :style="getImage(homeConfig.imgList.timeIcon)">
        <div class="time-title font_30">{{roundTimes}}</div>
      </div>
      <!--  砍价进度 -->
      <div class="bargain-container" v-if="showBargain">
        <div class="icon" :style="getImage(homeConfig.imgList.bargainTitleIcon)"></div>
        <div class="process-part"></div>
      </div>
      <!--    砍价按钮  -->
      <div class="bargain-btns flex_center_space"  :class="{ 'shift-down': showBargain }">
        <div class="left-bargain-btn" @click="toggleBargain" :style="getImage(homeConfig.imgList.bargaining)"></div>
        <div class="right-bargain-btn" @click="toggleBargain" :style="getImage(homeConfig.imgList.helpBargaining)"></div>
      </div>
      <!--   砍价数据   -->
      <div class="bargain-data" :class="{ 'shift-down': showBargain }"></div>
      <err-modal ref="errRef"></err-modal>
    </background>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import Background from '@/components/background/Background.vue';
import { buriedPoint, getImgSize } from '@/utils/common';
import { jumpTime } from '@/utils/debounce';
import ErrModal from '@/components/modal/errModal.vue';

import { userService } from '@/service/api';
import { globalConfig } from '@/utils/config';
import { getImage, isEmpty } from '@/utils/utils';
import store from '@/store';
import BackButton from '@/components/back/index.vue';
import { imageLoadedHandle, preLoadImage } from '@/utils/imageHandle';

// 类型定义导入
import type {
  HotAreaItem,
  StyleObject,
  HomeConfig,
  LocationInfo,
  ActivityConfig,
  ApiResponse,
  RouteConfigMap
} from '@/types/home';
import InitMask from '@/components/mask/InitMask.vue';

@Component({
  computed: {},
  methods: { getImage },
  components: { InitMask, BackButton, ErrModal, Background }
})
export default class home extends Vue {
  get bgLayout() {
    const bgImage = this.homeConfig.bgImage;

    return {
      layoutType: 'onePageTop',
      bgImage: [bgImage],
      bgColor: this.homeConfig.bgColor
    };
  }


  /**
   * 生成热区样式
   * @param item 热区配置项
   * @returns 样式对象
   */
  hotAreaList(item: HotAreaItem): StyleObject {
    const { position, img } = item;

    // 转换位置值为rem单位
    const convertToRem = (value: number): string => `${value / 100}rem`;

    // 处理right属性的特殊逻辑
    const getRightStyle = (right: number | undefined): string => {
      if (right === 0) return '0rem';
      return right ? convertToRem(right) : '';
    };

    // 基础样式对象
    const baseStyles = {
      width: convertToRem(position.width),
      height: convertToRem(position.height),
      right: getRightStyle(position.right),
      left: position.left ? convertToRem(position.left) : '',
      top: convertToRem(position.top),
      position: 'absolute' as const
    };

    // 如果有图片，添加背景相关样式
    if (img) {
      return {
        ...baseStyles,
        backgroundImage: `url(${img})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100% 100%',
        ...getImgSize(img)
      };
    }

    return baseStyles;
  }

  mobile = ''; // 手机号

  private homeConfig!: any;

  isReady = false;

  dataList!: HotAreaItem[];

  showBargain = true

  bagNum  = 0;

  private countdownTimer: any = null

  publishTime: string = ''

  roundTimes: string = '23:59'

  /**
   * 进度数值
   *
   */
  percent: any = 0


  /**
   * 切换砍价进度区域显示状态
   */
  toggleBargain(): void {
    this.showBargain = !this.showBargain;
  }

  @Ref('errRef') errRef: any;

  async created() {
    this.homeConfig = this.$configData.homePage;
    this.dataList = this.homeConfig.dataList;
    this.mobile = this.$microUtils.getToken('mobile');
    await imageLoadedHandle([this.homeConfig.bgImage], () => {
      // 接口调用
      this.isReady = true;
    });
    preLoadImage(Object.values(this.homeConfig.imgList));

    // this.verifyTime();
  }

   /**
     * 倒计时相关
     *
     */
  private parsePublishTimeToMs(val: any): number | null {
    console.log('🚀 ~ file:home method:parsePublishTimeToMs line:368 -----', val);
    if (!val) return null;
    if (typeof val === 'number') return val > 1e12 ? val : val * 1000;
    if (typeof val === 'string') {
      const t = Date.parse(val.replace(/-/g, '/'));
      return isNaN(t) ? null : t;
    }
    const ms = new Date(val as any).getTime();
    return isNaN(ms) ? null : ms;
  }

  private formatRemain(ms: number): string {
    const minutes = Math.floor(ms / 60000);
    if (minutes <= 0) return '已结束';
    const days = Math.floor(minutes / (60 * 24));
    const hours = Math.floor((minutes % (60 * 24)) / 60);
    const mins = minutes % 60;
    if (days > 0) return `${days}天${hours}小时${mins}分`;
    if (hours > 0) return `${hours}小时${mins}分`;
    return `${mins}分钟`;
  }

  startRoundCountdown(): void {
    this.clearCountdown();
    const target = this.parsePublishTimeToMs(this.publishTime );
    console.log('🚀 ~ file:home method:startRoundCountdown line:392 -----', target,this.publishTime);
    if (!target) {
      this.roundTimes = '--';
      return;
    }
    this.updateRoundTimes(target);
    this.countdownTimer = setInterval(() => {
      this.updateRoundTimes(target);
    }, 60 * 1000);
  }

  private updateRoundTimes(targetMs: number): void {
    const diff = targetMs - Date.now();
    if (diff <= 0) {
      this.roundTimes = '已结束';
      this.clearCountdown();
      return;
    }
    this.roundTimes = this.formatRemain(diff);
  }

  private clearCountdown(): void {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }


  /**
   * 获取活动列表并处理动态 banner 显示
   */
  async activityList() {
    const [res, err] = await userService.getActivityList({});
    if (err) {
      return this.showErr(err)
    }
    if(res){
      console.log('🚀 ~ file:res method:activityList line:50 -----', res);
    }
  }


  mounted() {
    // this.showErr();
    // this.openTipsDialog('location');
    // this.roastRef.open({showEnroll: false, showSuccess: true})
  }

  /**
   * 热区点击处理
   * @param item 点击的热区项
   */
  @jumpTime()
  itemClickHandle(item: HotAreaItem): void {
    // 埋点统计
    buriedPoint(item.brief);

    const { name, url } = item;

    // 规则和服务页面跳转
    if (name === 'rule' || name === 'service') {
      this.$router.push({
        path: '/rule',
        query: {
          url,
          isCover: name === 'service' ? 'T' : ''
        }
      });
      return;
    }

    // 奖品页面直接跳转
    if (name === 'prize') {
      this.$router.push({ path: `/${name}` });
      return;
    }
  }



  /**
   * 显示业务错误信息
   * @param err 错误对象
   * @param config 配置对象
   */
  showBErr(err: ApiResponse, config: ActivityConfig): void {
    const { rtn_flag, rtn_msg } = err;
    const errObj = config.errDialogConfig?.find((item: any) => item.code === rtn_flag);

    if (errObj?.title) {
      this.errRef.open(errObj);
    } else {
      const errorMessage = errObj?.tips || rtn_msg || '当前人数参与太多,请稍后再试！';
      this.$toast(errorMessage);
    }
  }

  /**
   * 显示通用错误信息
   * @param err 错误对象
   */
  showErr(err: ApiResponse): void {
    const { rtn_flag, rtn_msg } = err;
    const errObj = this.homeConfig.errDialogConfig?.find(
      (item: any) => item.code === rtn_flag
    );

    // 根据配置校验是否绑定户号
    const unBind = true;

    if (errObj?.title && unBind) {
      this.errRef.open(errObj);
    } else {
      const errorMessage = errObj?.tips || rtn_msg || '当前人数参与太多,请稍后再试！';
      this.$toast(errorMessage);
    }
  }

  /**
   * 打开提示弹窗
   * @param errFlag 错误标识
   */
  openTipsDialog(errFlag: string): void {
    const errObj = this.$configData.homePage.errDialogConfig?.find(
      (item: any) => item.flag === errFlag
    );

    if (errObj) {
      this.errRef.open(errObj);
    }
  }


   /**
     * 户号以及缴费示列
     *
     */
   isProvinceId = '14000' // 城市编码

    accountNumberList :any= []

  /**
   * 处理户号切换本地以及在线显示
   *
   */
  isHandledHousehold() {
    const isMock = this.$configData?.showMockDta;
    if(isMock){
      const householdList = this.$configData.mockData.showPowerFlag ? this.$configData.mockData.powerUserList : []
      this.handleHouseholdList(householdList)
      return ;
    }
    this.getHouseholdList();
  }

   getHouseholdList() {
    console.log(
      '🚀 ~ file:home method:getHouseholdList line:265 -----',
      this.$microUtils.getToken('Token')
    );
    console.log(
      '🚀 ~ file:home method:user_id line:265 -----',
      this.$microUtils.getToken('user_id')
    );
    // 获取户号列表
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取户号数据', // 方法或接口描述
      data: {
        url: '/osg-open-uc0001/member/c8/f68',
        type: '01',
        accessToken: this.$microUtils.getAccessToken(), // "asdweefwere121321e21e321",
        data: {
          token: this.$microUtils.getToken('Token'),
          uscInfo: {
            tenant: 'state_grid',
            member: this.$microUtils.configInfo.CLIENT_ID,
            devciceId: '',
            devciceIp: ''
          },
          quInfo: {
            userId: this.$microUtils.getToken('user_id'),
            provinceId: this.isProvinceId || '140000'
          }
        }
      } // 业务入参
    };
    this.$publics
      .JSBridgeMethod('NLRequest', param)
      .then((res: any) => {
        console.log(res, '户号列表');
        console.log(res.data, '户号列表res.data');
        console.log(res.data.bizrt.powerUserList, 'data');
        if (res.code == 1 && res.data && res.data.bizrt && res.data.bizrt.powerUserList) {
          const householdList = res.data.bizrt.powerUserList;
          this.handleHouseholdList(householdList)
        }
      })
      .catch((res: any) => {
        console.error(`${res}异常`);
      });
  }

  /**
   * 处理户号列表数据
   * @param householdList 响应数据
   */
  handleHouseholdList(householdList: any) {
    console.log('🚀 ~ file:home method:handleHouseholdList line:604 -----', householdList);
    this.accountNumberList = [];
    householdList.forEach((item: any) => {
      // 山西省的省份ID是140000
      if (item.provinceId == this.isProvinceId || item.provinceId == '140000') {
        this.accountNumberList.push({
          consNo: item.powerUserNo,
          orgNo: item.powerUnit,
        });
      }
    });

    // 更新绑定户号任务状态
    // this.taskStatusMap['bindAccount'] = this.accountNumberList.length > 0;

    // 设置默认选中第一个户号
    if (this.accountNumberList.length > 1) {
      // this.selectedHousehold = this.accountNumberList[0];
    }
    console.log('户号列表处理完成:', this.accountNumberList);

    // 户号数量分支处理
    const len = this.accountNumberList.length;
    if (len === 0) {
      // 1) 户号为空：提示绑定户号
      // this.$toast('请先绑定山西电力户号');
      this.openTipsDialog('unBind');
      return;
    }

    if (len === 1) {
      // 2) 户号仅一个：自动选择并提交任务完成

      return;
    }

    // 3) 户号多个：唤起底部选择弹框（默认选中第一个）
    // this.showHouseholdSelectModal();

  }

  /**
   * 处理缴费金额
   *
   */
  provincePowerId = '14101'

  isHandledPay() {
    const isMock = this.$configData.showMockDta;
    if(isMock){
      const householdList = this.$configData.mockData.showServiceFlag ? this.$configData.mockData.serviceRecordList : []
      this.handlePayList(householdList)
      return ;
    }
    this.getServiceRecord();
  }

  /**
   * 服务记录查询接口
   * 查间用户是否有满足条件的交费记录〔年轮周期范围内给山西电力户号单笔交费之5元)
   *
   */
  async getServiceRecord() {
    const param = {
      url: '', // 获取用户服务记录接口
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取用户服务记录接口', // 方法或接口描述
      data: {
        url: '/osg-open-uc0001/member/c8/f67',
        accessToken: this.$microUtils.getAccessToken(), // "asdweefwere121321e21e321",
        data: {
          serviceId: '',
          token: this.$microUtils.getToken('Token'),

          accountId: this.$microUtils.getToken('user_id'),
          pageNo: '1',
          pageSize: '100',
          busyTypeCode: '02',
          provinceId: this.isProvinceId || '140000',
          subBusyTypeCode: '0201',
          sSubBusyTypeCode: '020101',
          startTime: /*moment(this.currentActivity?.round?.startTime || '').format('YYYY-MM-DD HH:mm:ss') ||*/ this.$configData.homePage.dateItem.startTime || '', //周期时间
          endTime: /*moment(this.currentActivity?.round?.endTime || '').format('YYYY-MM-DD HH:mm:ss') ||*/ this.$configData.homePage.dateItem.endTime || '', // 结束时间
          uscInfo: {
            tenant: 'state_grid',
            member: this.$microUtils.configInfo.CLIENT_ID,
            devciceId: '',
            devciceIp: ''
          },
          quInfo: {
            userId: this.$microUtils.getToken('user_id'),
            provinceId: this.isProvinceId || '140000'
          }
        }
      }
    };

    this.$publics
      .JSBridgeMethod('NLRequest', param)
      .then((res: any) => {
        console.log(res, '服务记录查询接口');
        console.log(res.data, '服务记录查询接口data');

        // 处理服务记录数据，判断是否有满足条件的交费记录
        if (res.code == 1 && res.data ) {
          const serviceRecords = res.data ? res.data : res.data?.bizrt?.serviceRecordList || [];
          this.handlePayList(serviceRecords)
        }
      })
      .catch((res: any) => {
        console.error(`${res}异常`);
      });
  }

  /**
   * 处理服务记录数据：
   * 1) 仅保留xx省的记录（支持多种字段位置判断 provinceId/provinceCode/proConsNo.consNoList）
   * 2) 判断 serContent.payAmount（或 amount）是否 ≥ 指定阈值
   * 3) 命中则仅取第一条，使用该记录的 consNo 调用任务完成接口
   * 4) 否则弹窗提示
   */
  handlePayList(res: any, opts?: {
    shanxiProvinceId?: string;          // 山西省 provinceId（默认 140000）
    amountThreshold?: number;           // 金额阈值（默认 20）
    noMatchTip?: string;                // 无匹配记录提示文案
  }) {
    const {
      shanxiProvinceId = this.provincePowerId || '14101',
      amountThreshold = Number(this.$configData?.mockData?.payMoney || 20),
      noMatchTip = '未查询到符合条件的缴费记录'
    } = opts || {};

    const list: any[] = Array.isArray(res) ? res : [];

    // 过滤出山西省记录 + 金额满足阈值
    const matched = list.filter((record: any) => {
      const pid = record?.provinceId || record?.serContent?.provinceCode;
      const hasProvince = pid === shanxiProvinceId || (
        Array.isArray(record?.proConsNo?.consNoList) &&
        record.proConsNo.consNoList.some((c: any) => c?.provinceId === shanxiProvinceId)
      );
      if (!hasProvince) return false;

      const payStr = record?.serContent?.payAmount ?? record?.amount ?? record?.serContent?.amount;
      const pay = Number(payStr);
      return !Number.isNaN(pay) && pay >= amountThreshold;
    });

    if (matched.length > 0) {
      const first = matched[0];
      // 优先从 consNoList（匹配山西省）拿户号；其次取顶层 consNo；最后兜底 serContent.consNo
      const consNoFromList = Array.isArray(first?.proConsNo?.consNoList)
        ? first.proConsNo.consNoList.find((c: any) => c?.provinceId === shanxiProvinceId)?.consNo
        : undefined;
      const consNo = first?.consNo || consNoFromList || first?.serContent?.consNo;

      console.log('🚀 ~ file:home method:handlePayList line:821 -----',first);
      if (consNo) {
        const payInfo = {
          amount: first?.serContent?.payAmount ?? first?.amount,
          payTime: first?.serContent?.payTime,
          orderId: first?.serContent?.orderId,
          transId: first?.serContent?.transId
        };
        console.log('🚀 ~ file:home method:handlePayList line:830 -----', payInfo);
        // 使用命中的 consNo 调用任务完成接口
        // this.taskComplete({ consNo }, payInfo);
        return;
      }
    }

    // 未匹配到：弹窗提示
    this.openTipsDialog('unPay')
  }


}
</script>

<style scoped lang="less">

.bag-icon {
  position: absolute;
  top: 1.6rem;
  left: 0.23rem;
  .bag-title {
    margin: 0.5rem 0 0 0.12rem;
    font-family: AlibabaPuHuiTi;
    font-weight: 500;
    color: #FFFFFF;
    transform: skewX(-15deg);
    display: inline-block;
    >span {
      font-family: HelloFont ID ChangHei;
      font-weight: 400;
      color: #F2FF00;
    }
  }
}

.time-icon {
  position: absolute;
  top: 2.8rem;
  left: 0.23rem;
  .time-title {
    font-family: AlibabaPuHuiTi;
    font-weight: 800;
    line-height: 0.49rem;
    text-align: center;
    letter-spacing: 0;
    color: #FF4331;
  }
}

.bargain-container {
  margin-top: 6.59rem;
  margin-left: 1.38rem;
}


.bargain-btns {
  margin-top: 6.96rem;

}
// 砍价功能样式
.bargain-btns,
.bargain-data {
  // 添加过渡动画
  transition: transform 0.3s ease-in-out;
}

// 当砍价进度显示时，按钮和数据向下移动
.bargain-btns.shift-down
{
  margin-top: 0.43rem;
}
</style>
