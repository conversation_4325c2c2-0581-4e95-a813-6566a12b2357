// import { Base64 }  from 'js-base64';
// 公钥
const publicKey =
  "040fb8bf146e1cb845ca9f61e5797c54a794c1ea31d4e9e6dd93d105fd7913b09a7b0a8697727b6ad4c97202937f368fc3eee8346477a162da347b36fe70c970b9";
// 私钥
const privateKey =
  "9de7862ecc6ba6d4f4050d786d31590c6740dbbd22110c77cfcf2feeb943a92f";

const cipherMode = 1; // 1 - C1C3C2，0 - C1C2C3，默认为1
// tslint:disable-next-line:no-var-requires
const sm2 = require("sm-crypto").sm2;
//
/**
 *
 * 加密
 *
 */
export const sm2Encrypt = (value: any) => {
  console.log(value, "------sm2加密前------");
  const data = sm2.doEncrypt(
    /*JSON.stringify(*/ value /*)*/,
    publicKey,
    cipherMode
  );
  console.log(data, "-----sm2加密后-----");
  return "04" + data;
};
/**
 *
 * 解密
 *
 */
export const sm2Decrypt = (value: any) => {
  const da = sm2.doDecrypt(
    value.substr(2).toLocaleLowerCase(),
    privateKey,
    cipherMode
  );
  console.log(da, "sm2解密后1----------------");
  const data = JSON.parse(da);
  console.log(data, "sm2解密后2-----------");
  return data;
};
