<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content=""/>
    <meta name="keywords" content=""/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="format-detection" content="telephone=no, email=no"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=0"/>
    <link rel="stylesheet" href="https://gw.alipayobjects.com/as/g/antui/antui/10.1.32/rem/antui-all.css"/>
    <link rel="stylesheet" href="https://gw.alipayobjects.com/as/g/antui/antui/10.1.32/rem/widget/search.css">
    <title>hebei-money-saving-act</title>
    <script>
        var docEl = document.documentElement;
        docEl.style.fontSize = 100 / 750 * docEl.clientWidth + 'px';
        window.addEventListener('resize', function () {
            docEl.style.fontSize = 100 / 750 * docEl.clientWidth + 'px';
        });
    </script>
    <script type="text/javascript"
            src="https://webapi.amap.com/maps?v=1.4.15&key=986270d55599cef6134efef68117e3c7&plugin=AMap.Autocomplete&plugin=AMap.Geocoder"></script>
    <script src="./pageShowAgain.js"></script>
</head>
<body>
<script>
    !(function (win, b, d, a) {
        win[a] || (win[a] = {});
        win[a].config = {
            project: "PJ1755853769001"
        };
    })(
        window,
        document,
        "https://oss-static.bangdao-tech.com/biz-track/2.2.0/biz-track.min.js",
        "_bzt"
    );
</script>
<script src="https://oss-static.bangdao-tech.com/biz-track/2.2.0/biz-track.min.js"></script>
<noscript>
    <strong>We're sorry but h5-heating-liyin1 doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
</body>
</html>
