import { Base64 } from 'js-base64'
import { jsonrepair } from 'jsonrepair'

// 公钥
const publicKey =
  '0438825164147f581762bba048de0829b9a6d081219f67ac25ac3455ebe79888086c26cbbb6ec793f199643c72f58520d878171654a5bdd56633a678d9683c8d9c'
// 私钥
const privateKey =
  '3206cc876448b5a4326b1908272e6d4778d54129713fcc22a02f954be61071e'
// 签名key
const signatureKey =
  '9de7862ecc6ba6d4f4050d786d31590c6740dbbd22110c77cfcf2feeb943a92f'

const cipherMode = 0 // 1 - C1C3C2，0 - C1C2C3，默认为1
// tslint:disable-next-line:no-var-requires
const sm2 = require('sm-crypto').sm2
//
/**
 *
 * 加密
 *
 */
export const doEncrypt = (value: any) => {
  console.log('加密前值为', value)
  const data = '04' + sm2.doEncrypt(value, publicKey, cipherMode)
  return data
}

export const doSignature = async (value: any) => {
  // console.log(value);
  const data = sm2.doSignature(value, signatureKey, { hash: true, der: true })
  // console.log('**', await Promise.resolve(data));
  return Promise.resolve(data)
}
/**
 *
 * 解密
 *
 */
export const doDecrypt = (value: any) => {
  // console.log(value, '-------解密结果-------1');
  let data: any
  if (!value) {
    data = value
  } else {
    data = sm2.doDecrypt(value.substr(2), privateKey, cipherMode) // 解密结果
  }
  try {
    const da = JSON.parse(jsonrepair(Base64.decode(data)))
    // console.log(da, '-------解密结果-------2')
    return Promise.resolve(da)
  } catch (e) {
    // console.log('🚀 ~ file:sm2 method:doDecrypt line:53 -----', e);
    console.error(e)
  }
}
