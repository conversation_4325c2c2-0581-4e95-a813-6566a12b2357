/**
 * 首页相关类型定义
 */

// 位置信息接口
export interface Position {
  width: number;
  height: number;
  top: number;
  left?: number;
  right?: number;
}

// 热区配置项接口
export interface HotAreaItem {
  name: string;
  brief: string;
  url?: string;
  img?: string;
  position: Position;
}

// 样式对象接口
export interface StyleObject {
  width: string;
  height: string;
  top: string;
  left: string;
  right: string;
  position: 'absolute';
  backgroundImage?: string;
  backgroundRepeat?: string;
  backgroundSize?: string;
  [key: string]: any;
}

// 错误配置对象接口
export interface ErrorConfig {
  code: string;
  flag?: string;
  title?: string;
  tips?: string;
}

// 活动配置接口
export interface ActivityConfig {
  activity_id: string;
  errDialogConfig: ErrorConfig[];
}

// 首页配置接口
export interface HomeConfig extends ActivityConfig {
  bgImage: string;
  dataList: HotAreaItem[];
  testLnglat?: boolean;
  imgList?: any;
}

// 地理位置接口
export interface LocationInfo {
  lng: string;
  lat: string;
}

// API 响应接口
export interface ApiResponse {
  rtn_flag: string;
  rtn_msg: string;
  rtn_data?: any;
}

// 路由配置映射类型
export type RouteConfigMap = Record<string, ActivityConfig>;
