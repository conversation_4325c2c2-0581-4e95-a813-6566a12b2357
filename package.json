{"name": "hebei-money-saving-act", "version": "0.0.1", "description": "{{description}}", "scripts": {"serve:dev": "vue-cli-service serve --mode dev", "serve:prod": "vue-cli-service serve --mode prod", "analyzer": "use_analyzer=true npm run build:dev", "build:dev": "vue-cli-service build --mode dev", "build:prod": "node ./build/build.js && vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "report": "vue-cli-service build --report"}, "dependencies": {"@bangdao/apollo": "^1.1.0-alpha.18", "@bangdao/captcha-multi": "^0.2.9", "@bangdao/common": "^1.0.16", "@types/qs": "^6.5.3", "axios": "^0.19.0", "clipboard": "^2.0.11", "core-js": "^3.6.5", "fingerprintjs2": "^2.1.4", "gsap": "^3.12.5", "jquery": "^3.7.1", "js-base64": "^3.7.8", "jsencrypt": "^3.2.1", "jsonrepair": "^3.8.0", "loadjs": "^4.3.0-rc1", "lodash.clonedeep": "^4.5.0", "lottie-web": "^5.9.1", "moment": "^2.29.4", "sm-crypto": "^0.3.6", "swiper": "5.2.0", "tinyjs-plugin-extract": "^1.4.0", "tinyjs-plugin-ui": "^0.7.4", "uuid": "^10.0.0", "vant": "^2.12.31", "vconsole": "^3.14.6", "vue": "~2.6.11", "vue-class-component": "^7.0.2", "vue-property-decorator": "^8.1.0", "vue-router": "^3.0.3", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.0.1", "vuex-class": "^0.3.2"}, "devDependencies": {"@bangdao/eslint-config-vue": "^1.0.5", "@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-typescript": "~4.4.0", "@vue/cli-service": "~4.4.0", "husky": "^4.3.7", "less": "^3.10.2", "less-loader": "^5.0.0", "lint-staged": "^10.5.3", "prettier": "^1.19.1", "typescript": "4.3.5", "vue-template-compiler": "~2.6.11", "webpack-bundle-analyzer": "^4.8.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "keywords": [], "author": "bangdao-fe", "lint-staged": {"*.{js,vue}": ["prettier --write", "vue-cli-service lint", "git add"]}}