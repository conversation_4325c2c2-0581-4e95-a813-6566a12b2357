const cacheGroups = {
  vendors: {
    name: 'chunk-vendors',
    test: /[\\/]node_modules[\\/]/,
    priority: 2,
    reuseExistingChunk: true,
    enforce: true
  },
  vant: {
    name: 'chunk-vant',
    test: /[\\/]node_modules[\\/]vant[\\/]/,
    priority: 3,
    reuseExistingChunk: true,
    enforce: true
  },
  vue: {
    name: 'chunk-vue',
    test: /[\\/]node_modules[\\/]vue[\\/]/,
    priority: 4,
    reuseExistingChunk: true,
    enforce: true
  },
  coreJs: {
    name: 'chunk-coreJs',
    test: /[\\/]node_modules[\\/]core-js[\\/]/,
    priority: 5, // 模块越大建议优先级越高
    reuseExistingChunk: true,
    enforce: true
  },
  vconsole: {
    name: 'chunk-vconsole',
    test: /[\\/]node_modules[\\/]vconsole[\\/]/,
    priority: 6, // 匹配模块的权重,权重越高打包优先级越高
    reuseExistingChunk: true,
    enforce: true
  },
  common: {
    name: 'business-common',
    test: /[\\/]src[\\/]common[\\/]/,
    priority: 8, // 匹配模块的权重,权重越高打包优先级越高
    reuseExistingChunk: true,
    enforce: true
  },
  utils: {
    name: 'business-utils',
    test: /[\\/]src[\\/]utils[\\/]/,
    priority: 9, // 匹配模块的权重,权重越高打包优先级越高
    reuseExistingChunk: true,
    enforce: true
  },
  components: {
    name: 'business-components',
    test: /[\\/]src[\\/]components[\\/]/,
    priority: 7, // 匹配模块的权重,权重越高打包优先级越高
    reuseExistingChunk: true,
    enforce: true
  }
};

module.exports = {
  /**基本配置**/
  // 打包路径
  publicPath: process.env.NODE_ENV === 'production' ? '' : '',

  // 当运行 vue-cli-service build 时生成的生产环境构建文件的目录，默认是‘dist’
  outputDir: 'dist', // 版本号

  // 放置静态文件夹目录，包含：css\img\js
  assetsDir: 'static',

  // 关闭文件名哈希
  filenameHashing: false,

  // 生产环境是否要生成 sourceMap（.map文件）。设置为false如果运行时报错，输出的错误信息无法准确得知是哪里的代码报错，但可以极大的缩小打包大小。
  productionSourceMap: false,

  //提取 CSS 在开发环境模式下是默认不开启的，因为它和 CSS 热重载不兼容。然而，你仍然可以将这个值显性地设置为 true 在所有情况下都强制提取。
  css: {
    extract: {
      Type: true
    }
  },

  // dev环境下，webpack-dev-server 相关配置
  devServer: {
    port: 8080, // 开发运行时的端口
    host: '0.0.0.0', // 开发运行时域名，设置成'0.0.0.0',在同一个局域网下，如果你的项目在运行，同时可以通过你的http://ip:port/...访问你的项目
    https: false, //  是否启用 https
    open: true // npm run serve 时是否直接打开浏览器
  },

  // 会通过webpack.merge合并该配置
  configureWebpack: config => {
    config.optimization = {
      // splitChunks:把一些node_modules下的依赖、公共组件、工具方法等能够单独出来，不干扰业务程序
      splitChunks: {
        // cacheGroups 下可以可以配置多个组，每个组根据test设置条件，符合test条件的模块，就分配到该组。
        // 模块可以被多个组引用，但最终会根据priority来决定打包到哪个组中。
        chunks: 'all',
        minSize: 0,
        cacheGroups: cacheGroups
      }
    };
  },

  chainWebpack: config => {
    // 分析包文件 工具
    if (process.env.use_analyzer) {
      // 分析
      config
        .plugin('webpack-bundle-analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);
    }
    // if (isProd) {
    //   config.optimization.delete("splitChunks");
    // }
    // 防止多页面打包卡顿
    // config.plugins.delete("named-chunks");

    return config;
  }
};
