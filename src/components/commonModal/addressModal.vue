<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import Modal from '@/components/modal/Modal.vue';
import { lotteryImg } from '@/utils/constant';
import { buriedPoint, Validator } from '@/utils/common';
import { jumpTime } from '@/utils/debounce';

@Component({
  computed: {
    lotteryImg() {
      return lotteryImg;
    }
  },
  components: { Modal }
})
export default class addressModal extends Vue {
  @Ref('addressModal') addressModal: any;

  consignee_mobile = ''; // 手机号
  consignee = ''; // 收货人名称
  address = ''; // 详细地址
  coupon: any;

  open(coupon: any) {
    this.coupon = coupon;
    if (coupon.consignee) {
      this.consignee = coupon.consigneeName;
      this.consignee_mobile = coupon.consigneeMobile;
      this.address = coupon.consigneeAddress;
    }
    this.addressModal.show = true;
  }

  info() {
    let res = false;
    Validator().add(
      this.consignee_mobile,
      'checkEmpty',
      '请输入收件人联系方式'
    );
    Validator().add(
      this.consignee_mobile,
      'checkRule',
      '收件人联系方式格式不正确'
    );
    Validator().add(this.consignee, 'checkEmpty', '请输入您的姓名');
    Validator().add(this.consignee, 'checkNameRule', '姓名只能输入汉字');
    Validator().add(this.address, 'checkEmpty', '请输入您的详细地址');
    Validator().add(this.address, 'checkAddressRule', '地址格式不正确');
    res = Validator().check((message: any) => {
      this.$toast(message);
    });
    if (res) return;
    return true;
  }

  @jumpTime()
  submit() {
    buriedPoint('地址点击提交');
    if (!this.info()) return;
    this.addressModal.show = false;
    console.log(
      '🚀 ~ file:prize method:confirmUse line:109 -----',
      Object.assign({}, this.coupon, {
        consigneeName: this.consignee,
        consigneeMobile: this.consignee_mobile,
        consigneeAddress: this.address
      })
    );
    this.$emit(
      'submit',
      Object.assign({}, this.coupon, {
        consigneeName: this.consignee,
        consigneeMobile: this.consignee_mobile,
        consigneeAddress: this.address
      })
    );
  }

  closeModal() {
    this.coupon = {};
  }
}
</script>

<template>
  <div>
    <!--确认手机信息弹窗-->
    <modal
      class="address-modal"
      :bg-obj="{ background: lotteryImg.addressBg }"
      ref="addressModal"
      :show-close-button="false"
    >
      <div class="address-content">
        <div class="name">
          <van-field
            v-model="consignee"
            placeholder="请输入收件人姓名"
            maxlength="10"
            type="text"
            class="field-input"
          />
        </div>
        <div class="mobile">
          <van-field
            v-model="consignee_mobile"
            placeholder="请输入收件人联系方式"
            maxlength="11"
            type="tel"
            class="field-input"
          />
        </div>
        <div class="address">
          <van-field
            v-model="address"
            placeholder="请输入收件人详细地址"
            maxlength="100"
            class="field-input"
          />
        </div>
      </div>
      <div class="address-btn" @click="submit()"></div>
      <div
        @click="
          addressModal.show = false;
          closeModal();
        "
        class="close_w_h close"
      ></div>
    </modal>
  </div>
</template>

<style lang="less">
.address-content {
  .van-cell {
    background: transparent !important;
    font-size: 0.24rem;
    height: 0.72rem;
    color: #989898;
    padding: 0.1rem;
    border-radius: 0.11rem;
    box-shadow: 0px 0px 0px 0px rgba(185, 57, 48, 0.24);
    box-sizing: border-box;
    border: solid 1px #f84d1f;
  }

  .van-field__control::-webkit-input-placeholder {
    color: #989898;
  }
}
</style>

<style scoped lang="less">
.address-content {
  position: absolute;
  top: 4.87rem;
  left: 1.31rem;

  .mobile,
  .name {
    width: 4rem;
  }

  .address {
    width: 2.92rem;
    margin-left: 1.04rem;
  }

  .mobile,
  .address {
    margin-top: 0.8rem;
  }
}

.address-btn {
  position: absolute;
  top: 9.58rem;
  left: 1.1rem;
  width: 4.38rem;
  height: 1.01rem;
}

.close {
  position: absolute;
  top: 0;
  right: 0.5rem;
}
</style>
