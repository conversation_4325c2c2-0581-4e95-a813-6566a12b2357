import { Http } from "@bangdao/apollo";
import { IHttpRequest } from "@bangdao/apollo/es/type";
import { globalConfig } from "@/utils/config";
import {
  encryptHeader,
  decryptHeader,
  decyptData,
  encyptData
  //@ts-ignore
} from "@/common/bd66/index.js";
import EventBus from "@/utils/evnetBus";
import { myTrackEvent, GATEWAY_TYPE } from "@/common/xmp";
import { Toast } from "vant";
import store from "@/store";

import { doEncrypt, doDecrypt } from "@/utils/sm2";
const sm3 = require("sm-crypto").sm3;
// import { v4 as uuidv4 } from 'uuid'

export interface RemoteApiServiceDeps {
  configData: {
    isShowCheckTime?: boolean;
    isShowEncryption?: boolean;
  };
  microUtils: {
    getTimeStamp: () => number | undefined;
    getToken: (key: string) => string | null;
  };
}

export interface RemoteApiRuntimeConfig {
  retryCount?: number;         // 默认重试次数
  retryDelay?: number;         // 重试间隔(ms)
  cache?: boolean;             // 是否启用缓存
  cacheTTL?: number;           // 缓存有效期(ms)
  showLoading?: boolean;       // 统一Loading开关
  loadingText?: string;        // Loading文案
  timeout?: number;            // 请求超时时间(ms)
  enableTimeoutRetry?: boolean; // 是否启用超时重试
}

export interface RemoteApiRequestOptions extends Partial<RemoteApiRuntimeConfig> {
  signal?: AbortSignal;                          // 取消请求
  headers?: Record<string, string>;              // 额外头
  cacheKey?: string;                             // 自定义缓存Key
}

type RemoteResponseTuple<T = any> = Promise<[T | undefined, any | undefined]>;

interface CacheEntry<T = any> {
  t: number;
  data: T;
}

export default class RemoteApiService {
  private http: Http;
  private configData: RemoteApiServiceDeps["configData"];
  private microUtils: RemoteApiServiceDeps["microUtils"];
  private runtime: Required<RemoteApiRuntimeConfig>;
  private cache = new Map<string, CacheEntry<any>>();
  private activeRequests = new Set<AbortController>(); // 跟踪活跃请求

  constructor(deps: RemoteApiServiceDeps, runtime?: RemoteApiRuntimeConfig) {
    this.http = new Http(globalConfig.httpConfig);
    this.configData = deps.configData;
    this.microUtils = deps.microUtils;
    this.runtime = {
      retryCount: 0,
      retryDelay: 600,
      cache: false,
      cacheTTL: 30_000,
      showLoading: true,
      loadingText: "加载中...",
      timeout: 30000,           // 默认30秒超时
      enableTimeoutRetry: true, // 默认启用超时重试
      ...(runtime || {}),
    } as Required<RemoteApiRuntimeConfig>;
  }

  // 链式配置，返回一个新实例
  public withConfig(cfg: RemoteApiRuntimeConfig): RemoteApiService {
    const inst = new RemoteApiService({
      configData: this.configData,
      microUtils: this.microUtils,
    }, { ...this.runtime, ...cfg });
    // 共享缓存
    (inst as any).cache = this.cache;
    return inst;
  }

  // 便捷创建取消控制器
  public createAbortController(): AbortController {
    return new AbortController();
  }

  // 清理所有 loading 状态的方法
  private clearAllLoadingStates(): void {
    try {
      // 清理 Toast loading
      Toast.clear();

      // 发送全局事件，通知所有组件清理 loading 状态
      EventBus.$emit("clearAllLoading");

      // 取消所有活跃的请求
      this.activeRequests.forEach(controller => {
        try {
          controller.abort();
        } catch (e) {
          console.warn("Failed to abort request:", e);
        }
      });
      this.activeRequests.clear();

      console.log("All loading states cleared due to timeout");
    } catch (error) {
      console.error("Error clearing loading states:", error);
    }
  }

  // 检查是否为超时错误
  private isTimeoutError(error: any): boolean {
    return (
      error?.code === 'ECONNABORTED' ||
      error?.message?.includes('timeout') ||
      error?.message?.includes('Network Error') ||
      (error?.response?.status === undefined && error?.request)
    );
  }

  // 对外暴露：与旧post保持完全一致的签名与返回
  public post(options: IHttpRequest, req?: RemoteApiRequestOptions): RemoteResponseTuple<any> {
    return this.sendRemoteRequest(options, req);
  }

  // 内部统一发送器：拦截/超时/重试/取消/缓存/Loading/错误处理/加解密/Token/时间戳校验
  private async sendRemoteRequest(options: IHttpRequest, req?: RemoteApiRequestOptions) {
    const cfg = { ...this.runtime, ...(req || {}) };

    // 创建请求的 AbortController
    const requestController = req?.signal ? null : new AbortController();
    const effectiveSignal = req?.signal || requestController?.signal;

    // 如果是新创建的控制器，添加到活跃请求集合
    if (requestController) {
      this.activeRequests.add(requestController);
    }

    // Loading 管理
    const showLoading = cfg.showLoading;
    if (showLoading) {
      Toast.loading({
        message: cfg.loadingText,
        forbidClick: true,
        duration: 0,
      });
    }

    // 时间戳校验（兼容旧逻辑）
    const isShowCheckTime = !!this.configData?.isShowCheckTime;
    if (this.microUtils?.getTimeStamp && this.microUtils.getTimeStamp() && isShowCheckTime) {
      const oldTime: number = this.microUtils.getTimeStamp() as number;
      const curTime: number = new Date().getTime();
      // 30分钟
      if (Math.abs(curTime - oldTime) > 29 * 60 * 1000) {
        Toast.clear();
        EventBus.$emit("showBounced");
        //@ts-ignore 保持与旧逻辑一致
        return [undefined, undefined] as any;
      }
    }

    const { data } = options;
    const newOptions = { data: { ...data } };
    const isShowEncryption = !!this.configData?.isShowEncryption;

    // @ts-ignore
    const {
      method,
      sign_type = "",
      ...res
    } = newOptions.data;

    // if (!res.token) {
    //   res.token = (globalConfig.httpConfig as any).token;
    // }

    console.log("接口名称:--------", method);

    // 获取 sgccToken 用于 X-Authorization 请求头
    const sgccToken =
      (this.microUtils?.getToken && this.microUtils.getToken("authToken")) ||
      (store.state as any).isTokenData.sgccToken;

    const baseHeaders = {
      "Content-Type": "application/json;charset=utf-8",
      "X-Timestamp": new Date().getTime(),
      // "X-Nonce": uuidv4(),
      ...(sgccToken ? { "X-Authorization": sgccToken } : {}),
      ...(req?.headers || {}),
    };

    const requestHeader = isShowEncryption
      ? {
          ...baseHeaders,
          ...encryptHeader,
          ...decryptHeader,
        }
      : baseHeaders;

    // 构造入参：加密走 bd66/SM2/SM3；非加密恢复为原先明文结构
    let paramsData: any;
    if (isShowEncryption) {
      try {
        const plainPayloadStr = JSON.stringify({...res});
        console.log('🚀 ~ file:remoteApiService method:sendRemoteRequest line:216 -----', newOptions.data ,);
        const sign = sm3(plainPayloadStr);
        const encryData = await doEncrypt(plainPayloadStr);
        paramsData = {
          data: {
            method,
            data : { encryData, sign },
          },
        };
      } catch (e) {
        Toast.clear();
        console.error("请求参数加密失败:", e);
        return [undefined, { rtnFlag: "encrypt_error", rtnMsg: "请求加密失败" }] as any;
      }
    } else {
      // 非加密：保持之前形式（method + sign_type + data 明文）
      paramsData = {
        data: {
          method,
          sign_type,
          data: { ...res },
        },
      };
    }

    // 缓存Key
    const cacheKey = req?.cacheKey || `remote:${method}:${JSON.stringify(res)}`;
    if (cfg.cache) {
      const hit = this.cache.get(cacheKey);
      if (hit && Date.now() - hit.t < cfg.cacheTTL) {
        // 命中缓存直接返回
        setTimeout(() => Toast.clear(), 0);
        return [hit.data, undefined];
      }
    }

    const extendsOptions = {
      withCredentials: true,
      signal: effectiveSignal,
      timeout: cfg.timeout, // 添加超时配置
    };

    const doOnce = () => {
      // 创建超时 Promise
      const timeoutPromise = new Promise((_, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error(`Request timeout after ${cfg.timeout}ms`));
        }, cfg.timeout);

        // 如果请求被取消，清除超时定时器
        if (effectiveSignal) {
          effectiveSignal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
          });
        }
      });

      const httpPromise = this.http.post({
        ...paramsData,
        contentType: "application/json",
        extendsOptions,
        header: requestHeader
      });

      // 使用 Promise.race 实现超时控制
      return Promise.race([httpPromise, timeoutPromise]);
    };

    const attempt = async (): Promise<[any | undefined, any | undefined]> => {
      try {
        const res: any = await doOnce();
        const result = await this.promiseWapper(Promise.resolve(res), {
          type: "post",
          params: { ...paramsData }
        });

        const [data, err] = result;
        if (!err && cfg.cache) {
          this.cache.set(cacheKey, { t: Date.now(), data });
        }
        return result;
      } catch (err: any) {
        // 检查是否为超时错误
        if (this.isTimeoutError(err)) {
          console.error("Request timeout detected:", err);

          // 立即清理所有 loading 状态
          this.clearAllLoadingStates();

          // 如果启用了超时重试，返回特殊的超时错误标识
          if (cfg.enableTimeoutRetry) {
            return this.promiseWapper(Promise.reject({
              ...err,
              isTimeout: true,
              shouldRetry: true
            }), {
              type: "post",
              params: { ...paramsData }
            });
          }
        }

        return this.promiseWapper(Promise.reject(err), {
          type: "post",
          params: { ...paramsData }
        });
      }
    };

    // 重试机制
    let tries = 0;
    let last: [any | undefined, any | undefined] = [undefined, undefined];
    let isTimeoutRetry = false;

    while (tries <= (cfg.retryCount || 0)) {
      last = await attempt();
      const [, err] = last;

      if (!err) break;

      // 检查是否为超时错误且需要重试
      if (err?.isTimeout && err?.shouldRetry && cfg.enableTimeoutRetry) {
        isTimeoutRetry = true;
        console.log(`Timeout retry attempt ${tries + 1}/${(cfg.retryCount || 0) + 1}`);
      }

      tries += 1;
      if (tries <= (cfg.retryCount || 0)) {
        // 超时重试使用更长的延迟
        const retryDelay = isTimeoutRetry ? Math.max(cfg.retryDelay * 2, 2000) : cfg.retryDelay;
        await new Promise(r => setTimeout(r, retryDelay));
      }
    }

    // 清理请求控制器
    if (requestController) {
      this.activeRequests.delete(requestController);
    }

    return last as any;
  }

  // 迁移的 promiseWapper 方法
  private promiseWapper(
    $promise: any,
    { type, params }: any
  ): Promise<[any, any]> {
    try {
      console.log("接口请求：", type, params);
    } catch (error) {
      console.error(error, "getStorage error");
    }
    return new Promise((resolve, reject) => {
      $promise
        .then(async (res: any) => {
          Toast.clear();

          // 适配新的响应结构格式
          console.log("原始响应数据:", res);

          // 出参解密（如果需要）
          const isShowEncryption = !!this.configData?.isShowEncryption;
          let responseData = res;
          // 如果启用加密且响应数据需要解密（兼容两种返回格式）
          if (isShowEncryption) {
            try {
              let decryptedData: any;
              if (res?.encryData) {
                // 先用SM2解密，再用bd66解码
                decryptedData = await doDecrypt(res.encryData);
                console.log('🚀 ~ file:remoteApiService method: line:386 -----',params?.data.method );
                console.log('🚀 ~ file:remoteApiService method: line:387 -----', decryptedData);
              } else {
                // 直接bd66解码字符串
                decryptedData = decyptData(res);
              }
              responseData = decryptedData;
            } catch (decryptError) {
              console.error("响应数据解密失败:", decryptError);
              responseData = res; // 解密失败时使用原始数据
            }
          }

          console.log("处理后响应数据:", responseData);

          // 基于新的响应结构进行判断
          const { rtnFlag, rtnMsg, rtnData, success } = responseData;

          if (rtnFlag === 9999) {
            // 业务处理成功
            console.log("请求成功:", responseData);
            resolve([rtnData, undefined]);
          } else {
            // 业务处理失败
            console.log("请求失败:", responseData);

            // 埋点上报错误信息
            myTrackEvent("BizError", {
              type: GATEWAY_TYPE,
              method: params.data?.method || params.method,
              code: rtnFlag,
              message: rtnMsg || "接口异常",
              biz_content: GATEWAY_TYPE
            });
            //@ts-ignore
            if (rtnFlag == this.configData.code || rtnFlag == this.configData.otherCode) {
              EventBus.$emit("showBounced");
              //@ts-ignore
              return [undefined, undefined];
            }

            resolve([
              undefined,
              {
                rtn_flag: rtnFlag || "fail",
                rtn_msg: rtnMsg || "不好意思，出错啦\n请稍后再来～～"
              }
            ]);
          }
        })
        .catch((err: any) => {
          // 检查是否为超时错误
          if (this.isTimeoutError(err) || err?.isTimeout) {
            console.error("请求超时:", err);

            // 确保清理所有 loading 状态
            this.clearAllLoadingStates();

            // 超时错误的特殊处理
            myTrackEvent("BizError", {
              type: GATEWAY_TYPE,
              method: params.data?.method || params.method,
              message: "Request timeout",
              code: "TIMEOUT",
              biz_content: GATEWAY_TYPE
            });

            console.info(
              "%c remote api request timeout",
              "color: #FF6B6B; font-weight: bold",
              err
            );

            resolve([
              undefined,
              {
                rtnFlag: "timeout_error",
                rtnMsg: "请求超时，请检查网络连接后重试",
                isTimeout: true,
                canRetry: true
              }
            ]);
            return;
          }

          Toast.clear();

          // 网络错误或其他异常
          console.error("请求异常:", err);

          myTrackEvent("BizError", {
            type: GATEWAY_TYPE,
            method: params.data?.method || params.method,
            message: err?.message || err || "网络异常",
            biz_content: GATEWAY_TYPE
          });

          console.info(
            "%c remote api request failed",
            "color: #DC143C; font-weight: bold",
            err
          );

          resolve([
            undefined,
            {
              rtnFlag: "network_error",
              rtnMsg: err?.message || err || "网络连接异常，请稍后重试"
            }
          ]);
        });
    });
  }

  // 公共方法：手动清理所有 loading 状态
  public clearAllLoading(): void {
    this.clearAllLoadingStates();
  }

  // 公共方法：取消所有活跃请求
  public cancelAllRequests(): void {
    this.activeRequests.forEach(controller => {
      try {
        controller.abort();
      } catch (e) {
        console.warn("Failed to abort request:", e);
      }
    });
    this.activeRequests.clear();
    this.clearAllLoadingStates();
  }
}

// 单例管理（可选全局使用）
export const RemoteApiServiceSingleton = (() => {
  let instance: RemoteApiService | undefined;
  return {
    init(deps: RemoteApiServiceDeps, runtime?: RemoteApiRuntimeConfig) {
      if (!instance) instance = new RemoteApiService(deps, runtime);
      return instance;
    },
    get() {
      if (!instance) throw new Error("RemoteApiService not initialized. Call RemoteApiServiceSingleton.init(...) first.");
      return instance;
    },
    // 全局清理方法
    clearAllLoading() {
      if (instance) {
        instance.clearAllLoading();
      }
    },
    cancelAllRequests() {
      if (instance) {
        instance.cancelAllRequests();
      }
    }
  };
})();
