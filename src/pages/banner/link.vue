<template>
  <div class="rules">
    <loading v-if="showLoading" />
    <iframe
      frameborder="0"
      @load="showLoading = false"
      @error="showLoading = false"
      :src="url"
      allowfullscreen
      webkitallowfullscreen
      mozallowfullscreen
    ></iframe>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import BackButton from '@/components/back/index.vue';
import { globalConfig } from '@/utils/config';
import Loading from '@/components/loading/Loading.vue';

@Component({
  components: { Loading, BackButton }
})
export default class Rules extends Vue {
  @Ref('iframe') iframeRef: any;
  url: any = ''; // 活动链接
  showLoading = true;
  created() {
    // 配置项
    console.log('活动链接:', this.$route.query);
    const isShowConsole = this.$route.query.isShowConsole || 'F'; // 是否展示调试工具
    this.url = `${this.$route.query.url}?token=${this.$microUtils.getToken(
      'Token'
    ) || globalConfig.httpConfig.token}${
      isShowConsole === 'T' ? '&debug=true' : ''
    }`;
    console.log('====🚀====测试=====🚀=====', this.url);
    window.addEventListener('message', event => {
      this.postMessageFun(event.data);
    });
  }

  // 通信消息处理
  postMessageFun(data: any) {
    switch (data.type) {
      case 'toPay':
        this.linkTo(data.data);
        break;
      case 'toOrder':
        this.jumpOrder(data.data);
        break;
      case 'toJump':
        this.linkJump(data.data);
        break;
      case 'back':
        // 老banner重定向去banner ,特色专区重定向去专区首页
        this.$router.replace({
          name: 'home'
        });
        break;
      case 'tokenExpired':
        // token 失效退出微应用
        WSGWBridge.callNativeFunction('close', {}, function callback(
          response: any
        ) {
          console.log('返回', response);
        });
        break;
    }
  }

  linkTo(menuId: any) {
    //交费页 B10070100
    this.$publics
      .JSBridgeMethod('jumpToMenu', { data: { menuId } })
      .then((res: any) => {
        console.log('jumpToMenu 反参' + res);
        console.log('jumpToMenu 反参' + JSON.stringify(res));
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  linkJump(param: any) {
    console.log('🚀 ~ file:link method:linkJump line:87 -----', param);
    WSGWBridge.callNativeFunction('jumpToMenu', param, function callback(
      res: any
    ) {
      console.log('jumpToMenu 反参' + res);
    });
  }

  jumpOrder(orderId: any) {
    (window as any).WSGWBridge.callNativeFunction(
      'setTempCache',
      {
        key: 'B10080000_jumpToPage',
        value: {
          page: 'myIntegralOrderDetail',
          orderChildId: orderId //子订单ID
        }
      },
      (res: any) => {}
    );
    const param1 = {
      menuId: 'B10080000', //菜单号
      url: '/index.html' //菜单号里面的路径，例如index.html
    };
    (window as any).WSGWBridge.callNativeFunction(
      'jumpToMenu',
      param1,
      function callback(res: any) {}
    );
  }

  goBack() {
    this.postMessageFun({ type: 'back' });
  }
}
</script>

<style lang="less" scoped>
.rules {
  iframe {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
.back-box {
  width: 100%;
  height: 0.44rem;
  background-image: url('https://sdr-admin-siteease.bangdao-tech.com/siteEasy/ossImg/1655437843554.png');
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #fff;
  text-align: center;
  box-sizing: border-box;
  .back-icon {
    width: 0.22rem;
    height: 0.22rem;
    display: flex;
    margin-right: 0.04rem;
    margin-left: 0.12rem;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .page-name {
    color: #333333;
    font-family: 'PingFangSC-Medium';
    font-size: 0.18rem;
    font-weight: 400;
    text-align: center;
  }
}
</style>
