<template>
  <div>
    <van-popup
      :lock-scroll="false"
      class="popup"
      :position="position"
      :close-on-click-overlay="false"
      v-model="show"
    >
      <div
        class="popup__bg"
        :style="{
          backgroundImage: `url(${bgObj.background})`,
          ...$getImgSize(bgObj.background)
        }"
      >
        <slot></slot>
      </div>
      <!--关闭按钮-->
      <div v-if="showCloseButton" class="popup__closeArea">
        <div
          @click="(show = false), close();"
          :style="{ backgroundImage: `url(${IMAGE.CLOSE})` }"
          class="popup__closeArea__close"
        ></div>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit, Watch } from 'vue-property-decorator';
import { State } from 'vuex-class';
@Component({})
export default class Modal extends Vue {
  @State('IMAGE') IMAGE: any;
  show = false; // 弹窗开关
  @Prop({ default: 'center', type: String }) position: any;
  @Prop({ default: '', type: Object }) bgObj: any; // 背景图属性
  @Prop({ default: true, type: Boolean }) showCloseButton: any; // 关闭按钮开关
  @Emit()
  private close() {
    return;
  }

  // 弹窗展示禁止背景滚动的兼容处理，记录当前滚动位置
  private top = 0;

  @Watch('show')
  visibleChange(val: any) {
    //兼容处理，弹窗出现背景可滚动的场景
    const $mainPage = document.querySelector('.app-root') as HTMLElement;
    if (val) {
      this.top = window.scrollY;
      $mainPage.style.position = 'fixed';
      $mainPage.style.top = -this.top + 'px';
    } else {
      $mainPage.style.position = '';
      $mainPage.style.top = '';
      $mainPage.style.zIndex = '1';
      // 回到原先的top
      window.scrollTo(0, this.top);
    }
  }
}
</script>
<style scoped lang="less">
::v-deep .van-overlay {
  z-index: 999 !important;
}
.popup {
  background-color: transparent;
  overflow-y: inherit;
  z-index: 999 !important;
  &__bg {
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  &__closeArea {
    width: 100%;
    margin-top: 0.2rem;
    display: flex;
    justify-content: center;
    &__close {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.62rem;
      height: 0.62rem;
    }
  }
}
</style>
