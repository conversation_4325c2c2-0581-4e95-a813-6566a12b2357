const fs = require("fs");

//返回package的json数据
function getPackageJson() {
  let data = fs.readFileSync("./package.json"); //fs读取文件
  return JSON.parse(data); //转换为json对象
}
// 版本号自增逻辑
function newV(version) {
  const stage = 10;
  const inc = 1; // 递增值
  // 版本号是字符串，所以要做一个转数字处理
  let arr = version.split(".").map((x) => +x);
  // 从最后一位计算自增，所以 i--
  for (let i = arr.length - 1; i >= 0; i--) {
    // 每一节数字大于设定的值 并且 不是第一位，给前一位进 1；
    if (arr[i] + 1 >= stage && i > 0) {
      arr[i - 1] += inc;
      arr[i] = 0; // 当前位从 0 计数
      if (arr[i - 1] >= stage && i - 1 > 0) {
        arr[i - 2] += inc;
        arr[i - 1] = 0; // 当前位从 0 计数
      }
    } else {
      arr[i] += inc;
    }
    break; // 结束循环
  }
  return arr.join(".");
}
//获取package的json
let packageData = getPackageJson();
packageData.version = newV(packageData.version);
//用packageData覆盖package.json内容
fs.writeFile(
  "./package.json",
  JSON.stringify(packageData, null, "\t"),
  (err) => {}
);
