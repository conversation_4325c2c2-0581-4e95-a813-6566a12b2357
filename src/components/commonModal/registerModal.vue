<script lang="ts">
import { Vue, Component, Ref, Prop } from 'vue-property-decorator';
import Modal from '@/components/modal/Modal.vue';
import { jumpTime } from '@/utils/debounce';
import { userService } from '@/service/api';
import { getRsaCode } from '@/common/rsa';
//@ts-ignore
import { Verify } from '@bangdao/captcha-multi/packages/vue';
import ConfirmModal from '@/components/commonModal/confirmModal.vue';
import { lotteryImg } from '@/utils/constant';

@Component({
  computed: {
    lotteryImg() {
      return lotteryImg;
    }
  },
  components: { ConfirmModal, Modal, Verify }
})
export default class registerModal extends Vue {
  @Ref('registerModal') registerModal: any;
  @Ref('verify') verify: any; // 滑块
  @Ref('confirmModal') confirmModal: any; // 滑块

  coinNum = 2;
  showSign = false; //是否展示签到弹框
  //
  gameObj: any = {
    mobile: '',
    tool_num: 0
  };

  private rule = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/; // 手机号格式校验
  private formData = {
    centerCode: '',
    tel: '', // 手机号
    code: '', // 验证码
    key: '' // 验证码校验key
  };
  private time: number = 60 * 1000; // 1min倒计时
  private remain = false; // 是否显示倒计时

  open(obj: any) {
    this.gameObj = obj;
    this.registerModal.show = true;
  }

  /**
   * 手机号处理
   */
  get newMobile() {
    const mobile = (this.gameObj.mobile &&
      `${this.gameObj.mobile.substr(0, 3)}-${this.gameObj.mobile.substr(
        -3,
        3
      )}`.split('-')) || ['', ''];
    return mobile;
  }

  /**
   * 手机号输入框监听
   */
  mobileListener() {
    this.formData.tel = `${this.newMobile[0]}${this.formData.centerCode}${this.newMobile[1]}`;
  }

  /**
   * 发送验证码
   */
  @jumpTime()
  async useVerify() {
    if (!this.formData.tel) return this.$toast('请输入手机号');
    if (!this.rule.test(this.formData.tel))
      return this.$toast('请输入正确的手机号');
    this.verify.refresh();
    this.verify.show();
  }

  /**
   * 滑块成功回调
   */
  @jumpTime()
  success() {
    // 刷新验证码
    this.verify.refresh();
    this.getSmsCode();
  }

  /**
   * 获取短信验证码
   */
  async getSmsCode() {
    const { activity_id, errDialogConfig } = this.$configData.homePage;
    const [, err] = await userService.userSmsCode({
      relate_id: activity_id,
      activity_id: activity_id,
      busi_type: 'SPFAN',
      item_type: 'SPFAN',
      mobile: getRsaCode(this.formData.tel)
    });
    console.log(
      '🚀 ~ file:registerModal method:getSmsCode line:103 -----',
      errDialogConfig
    );
    if (err) {
      const { rtn_msg, rtn_flag } = err;
      const flag = errDialogConfig.find((item: any) => item.code === rtn_flag)
        ?.tips;
      return this.$toast(flag ?? rtn_msg ?? '系统繁忙，请稍候再试');
    }
    this.remain = true;
  }

  /**
   * 验证相关信息
   */
  validate() {
    if (!this.formData.tel) return this.$toast('请输入手机号') && false;
    if (!this.rule.test(this.formData.tel))
      return this.$toast('请输入正确的手机号') && false;
    if (!this.formData.code) return this.$toast('请输入验证码') && false;
    if (!(this.formData.code.length === 6))
      return this.$toast('请输入正确的验证码') && false;
    return true;
  }

  @jumpTime()
  async submit() {
    // 验证信息不过直接return
    if (!this.validate()) return;
    this.registerModal.show = false;
    this.confirmModal.open({
      mobile: this.formData.tel,
      code: this.formData.code
    });
  }

  backRegister() {
    this.registerModal.show = true;
  }
  bindSuccess() {
    console.log('🚀 ~ file:registerModal method:bindSuccess line:137 -----');
    this.registerModal.show = false;
    this.$emit('bindSuccess');
  }
}
</script>

<template>
  <div>
    <!--  注册手机  -->
    <modal
      :bg-obj="{ background: lotteryImg.mobileDialog }"
      class="lottery-modal"
      ref="registerModal"
      :show-close-button="false"
    >
      <div class="register">
        <!--手机号表单-->
        <div class="register__form">
          <div class="register__form-new">
            <span>{{ newMobile[0] }}</span
            ><span>{{ newMobile[1] }}</span>
          </div>
          <van-field
            @input="mobileListener"
            class="register__form__mobile"
            v-model="formData.centerCode"
            maxlength="5"
            input-align="center"
            placeholder="请输入中间5位数"
            type="tel"
          />
          <van-field
            class="register__form__code top"
            v-model="formData.code"
            maxlength="6"
            placeholder="请输入验证码"
            type="number"
          >
            <button slot="button" class="register__form__code__form">
              <span @click="useVerify" v-if="!remain">获取验证码</span>
              <van-count-down v-else @finish="remain = false" :time="time">
                <template #default="timeData">
                  <span class="block">{{ timeData.seconds }}s</span>
                </template>
              </van-count-down>
            </button></van-field
          >
        </div>
        <div @click.stop="submit" class="register__confirm"></div>
      </div>
      <div class="close close_w_h" @click="registerModal.show = false"></div>
      <!--滑块组件-->
      <Verify
        @success="success"
        :mode="'pop'"
        :vSpace="10"
        baseUrl="https://oapi.bangdao-tech.com/gateway.do"
        :captchaType="'blockPuzzle'"
        :imgSize="{ width: '280px', height: '160px' }"
        ref="verify"
      ></Verify>
    </modal>
    <confirm-modal
      ref="confirmModal"
      @backRegister="backRegister"
      @bindSuccess="bindSuccess"
    ></confirm-modal>
  </div>
</template>

<style scoped lang="less">
.lottery-modal {
  .close {
    position: absolute;
    top: 0;
    right: 0.5rem;
  }
  .register {
    &__confirm {
      width: 3.78rem;
      height: 1.01rem;
      position: absolute;
      left: 0;
      right: 0;
      margin: 0 auto;
      top: 6.5rem;
    }
    &__form {
      position: absolute;
      left: 0;
      right: 0;
      margin: 0 auto;
      top: 3.34rem;
      ::v-deep .van-field__control {
        height: 0.86rem;
        font-size: 0.28rem;
      }
      &-new {
        font-family: Alibaba PuHuiTi;
        position: absolute;
        font-size: 0.42rem;
        color: #383838;
        font-weight: 600;
        left: 0;
        right: 0;
        top: 0.3rem;
        display: flex;
        justify-content: space-between;
        width: 4.5rem;
        margin: 0 auto;
      }
      &__mobile {
        width: 2.44rem;
        height: 0.86rem;
        position: absolute;
        border: 1px solid #333333;
        border-radius: 0.09rem;
        left: 0;
        right: 0;
        top: 0.1rem;
        margin: 0 auto;
        padding: 0;
      }
      &__code {
        width: 4.38rem;
        height: 0.86rem;
        border: 1px solid #333333;
        border-radius: 0.09rem;
        position: absolute;
        left: 0;
        right: 0;
        top: 0.1rem;
        margin: 0 auto;
        padding: 0 0 0 0.2rem;
        &.top {
          top: 1.3rem;
        }
        &__form {
          width: 1.88rem;
          height: 0.86rem;
          font-family: Alibaba PuHuiTi;
          font-size: 0.24rem;
          color: #ffffff;
          line-height: 0.27rem;
          text-align: center;
          background-color: #44bdf7;
          border-radius: 0px 0.09rem 0.09rem 0px;
          border: none;
          .block {
            color: #ffffff;
          }
        }
      }
    }
  }
}
</style>
