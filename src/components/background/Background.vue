<template>
  <div
    class="page"
    :class="[pageLayoutClass]"
    :style="{ backgroundColor: bgColor }"
  >
    <!-- 内容展示 -->
    <div
      class="page-content"
      :style="{
        height:
          layoutType === 'imageSizeTop' && pageHeight
            ? `${pageHeight / 100}rem`
            : '',
        backgroundImage: backgroundImageCss
      }"
    >
      <!-- 热区定位容器，默认高度7.5rem -->
      <div class="hot-area-wrap">
        <slot></slot>
      </div>
      <slot v-if="$slots.content" name="content"></slot>
      <template v-else-if="bg2img">
        <img
          v-for="(item, index) in bgImageArr"
          :key="index + '-' + item"
          class="bg-image"
          :src="item"
        />
      </template>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';

@Component({})
export default class Background extends Vue {
  // 页面布局类型 onePageTop：一屏贴顶【背景图片：750*1496,透明标题栏：750*1624】，imageSizeTop：贴顶（图长可滚动）【背景图片：750*1206+】，onePageMiddle：一屏居中（废弃）【背景图片：750*1496,透明标题栏：750*1624】
  @Prop({
    type: String,
    default: 'onePageTop'
  })
  private layoutType!: string;

  // 页面高度 非必填，默认页面由内容图片撑开，设置高度则取高度
  @Prop()
  private pageHeight!: number;

  // 背景图片
  @Prop()
  private bgImage!: string | string[];

  // 背景颜色
  @Prop({
    type: String,
    default: '#fff'
  })
  private bgColor!: string;

  // 背景图片数组格式整理，便于遍历
  private get bgImageArr() {
    return (typeof this.bgImage === 'string'
      ? [this.bgImage]
      : this.bgImage
    ).filter(image => !!image);
  }
  // 背景图片使用背景实现，否则使用img
  private get bg2img() {
    if (this.layoutType === 'imageSizeTop' && this.$slots.content) {
      return false;
    } else {
      return true;
    }
  }
  // backgroundImageCss
  private get backgroundImageCss() {
    return this.bg2img
      ? ''
      : this.bgImageArr.map(image => `url('${image}')`).join(',');
  }
  // 格式化布局class
  private get pageLayoutClass() {
    return (this.layoutType || '').toLowerCase() + '-layout';
  }

  mounted() {
    // 设置背景色
    document.getElementsByTagName(
      'html'
    )[0].style.backgroundColor = this.bgColor;
    document.getElementsByTagName(
      'body'
    )[0].style.backgroundColor = this.bgColor;
  }
  destroyed() {
    document.getElementsByTagName('html')[0].style.backgroundColor = '';
    document.getElementsByTagName('body')[0].style.backgroundColor = '';
  }
}
</script>
<style scoped lang="less">
.page {
  margin: 0;
  padding: 0;
  font-size: 0;
  .hot-area-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // pointer-events: none;
  }
}
/* 背景图片一屏平铺 */
.page.onepagetop-layout {
  position: relative;
  width: 100%;
  //height: 100vh;
  min-height: 12.06rem;
  //overflow: hidden;
}
.page.onepagetop-layout .page-content {
  position: relative;
  width: 100%;
}

.bg-image {
  width: 100%;
  height: auto;
}
</style>
