import Axios from 'axios';
// import { pagesInfo } from '@/utils/pages.config';
import { GATEWAY_TYPE, myTrackEvent } from '@/common/xmp';

// 获取网关域名
export function getGatewayUrl() {
  let GATEWAY_URL = '';
  const HOST_NAME = window.location.hostname;
  switch (HOST_NAME) {
    case 'gateway.bangdao-tech.com':
      GATEWAY_URL = 'http://gateway.bangdao-tech.com:30001/gateway.do';
      break;
    case 'release-ali.bangdao-tech.com':
    case 'testali.bangdao-tech.com':
    case 'release-openapi.bangdao-tech.com':
      GATEWAY_URL = 'https://release-openapi.bangdao-tech.com/gateway.do';
      break;
    case 't-ali.bangdao-tech.com':
      GATEWAY_URL = 'https://openapi.bangdao-tech.com/gateway.do';
      break;
    case 'ali.bangdao-tech.com':
    case 'm.bangdao-tech.com':
    case 'oapi.bangdao-tech.com':
      GATEWAY_URL = 'https://oapi.bangdao-tech.com/gateway.do';
      break;
    default:
      GATEWAY_URL = process.env.VUE_APP_BASE_LOCAL_URL;
      // GATEWAY_URL = "https://test-oapi.bangdao-tech.com/gateway.do";
      // GATEWAY_URL = "https://oapi.bangdao-tech.com/gateway.do";
      break;
  }
  return GATEWAY_URL;
}

export const globalConfig = {
  httpConfig: {
    appId: '2015121300967512',
    getWayURL: getGatewayUrl(),
    platform: 'h5',
    token: '99tt2aec4d17540748d5a45f30cda92fd8c6',
    userId: '97ua0568bd3019fc4bebb4c7661fde01'
  },
  /**
   *  loopCode token 过期时是否要再次请求token 类型为 array,
   * 不配置则不自动请求 token (只支持邦道网关，不支持本地化部署)
   *
   * loopCode token 失败的错误码
   *
   * rtnCode 网关成功码
   * rtnKes 取 rtnCode 路径
   *
   * bizCode 业务请求成功码
   * bizKeys 取 bizCode 路径
   *
   * ******** 与后端约定 code 最好数字组好，不要用单词 *********
   *
   * TODO: 如果您已经理解，请删除这一坨注释
   */
  standardHttpConfig: {
    rtnCode: [200], // 网关 code
    bizCode: ['9999'], // 服务 code
    bizKeys: ['content', 'rtn_flag'], // 服务 key
    rtnKes: ['ret_code'], // 网关key
    loopCode: [5006, 5007] // 授权失败key
  }
};

// 获取内容投放平台配置项数据
export function getConfigData() {
  const gatewayUrl = process.env.VUE_APP_BASE_URL;
  const adUrl = `${
    process.env.VUE_APP_CONFIG_URL
  }?date=${new Date().getTime()}`;
  console.log(
    '🚀 ~ file:config method:getConfigData line:71 -----',
    process.env,
    adUrl,
    getGatewayUrl()
  );
  return new Promise(function(resolve) {
    Axios.get(adUrl).then((res: any) => {
      resolve(res);
    });
  });
}

const loadjs = require('loadjs');

export function loadJsFun($versionNum: any, pagesInfo: any) {
  return new Promise(function(resolve) {
    // 假设当前远程服务器的最新版本号是0.0.2
    const latestVersion = $versionNum; // 此版本号在赋能平台配置
    // 远程服务器请求域名
    const realmName = process.env.VUE_APP_SERVICE_URL;
    // 生成所有业务js文件路径，类似：http://psm-static.bangdao-tech.com/app/h5-beijing-revelry/0.0.1/static/js/xxx.js
    const jsArr: string[] = [];
    Object.keys(pagesInfo).forEach((item: string) => {
      const jsUrl = `${realmName}${latestVersion}/static/js/${item}.js`;
      jsArr.push(jsUrl);
    });
    // 加载公共业务组件的相关代码包
    jsArr.push(`${realmName}${latestVersion}/static/js/business-components.js`);
    jsArr.push(`${realmName}${latestVersion}/static/js/business-common.js`);
    jsArr.push(`${realmName}${latestVersion}/static/js/business-utils.js`);

    console.log('jsArr2', jsArr);
    // 定义bundle
    loadjs(jsArr, 'jsFile');
    // 定义回调
    loadjs.ready('jsFile', {
      success: function(res: any) {
        /*  文件加载成功 回调 */
        console.log('====🚀=====加载js====🚀=====');
        resolve(res);
      },
      error: function(pathsNotFound: any) {
        myTrackEvent('BizError', {
          type: GATEWAY_TYPE,
          method: 'load.fail',
          code: '0000',
          message: '动态加载异常',
          biz_content: GATEWAY_TYPE
        });
        /* 文件加载失败 回调 */
        if (
          pathsNotFound &&
          pathsNotFound.indexOf &&
          pathsNotFound.indexOf('jsFile') > -1
        ) {
          console.log('js文件load失败');
        }
        if (
          pathsNotFound &&
          pathsNotFound.indexOf &&
          pathsNotFound.indexOf('cssFile') > -1
        ) {
          console.log('css文件load失败');
        }
        resolve(pathsNotFound);
      }
    });
  });
}

export function loadCss($versionNum: any, pagesInfo: any) {
  return new Promise(function(resolve) {
    // 假设当前远程服务器的最新版本号是0.0.2
    const latestVersion = $versionNum; // 此版本号在赋能平台配置
    // 远程服务器请求域名
    const realmName = process.env.VUE_APP_SERVICE_URL;
    // 生成所有业务js文件路径，类似：http://psm-static.bangdao-tech.com/app/h5-beijing-revelry/0.0.1/static/js/xxx.js
    const cssArr: string[] = [];
    Object.keys(pagesInfo).forEach((item: string) => {
      const cssUrl = `${realmName}${latestVersion}/static/css/${item}.css`;
      cssArr.push(cssUrl);
    });
    // 加载公共业务组件的相关代码包
    cssArr.push(
      `${realmName}${latestVersion}/static/css/business-components.css`
    );
    loadjs(cssArr, 'cssFile');
    loadjs.ready('cssFile', {
      success: function(res: any) {
        /*  文件加载成功 回调 */
        console.log('====🚀=====加载css====🚀=====');
        resolve(res);
      },
      error: function(pathsNotFound: any) {
        myTrackEvent('BizError', {
          type: GATEWAY_TYPE,
          method: 'load.fail',
          code: '0000',
          message: '动态加载异常',
          biz_content: GATEWAY_TYPE
        });
        /* 文件加载失败 回调 */
        if (
          pathsNotFound &&
          pathsNotFound.indexOf &&
          pathsNotFound.indexOf('jsFile') > -1
        ) {
          console.log('js文件load失败');
        }
        if (
          pathsNotFound &&
          pathsNotFound.indexOf &&
          pathsNotFound.indexOf('cssFile') > -1
        ) {
          console.log('css文件load失败');
        }
        resolve(pathsNotFound);
      }
    });
  });
}
