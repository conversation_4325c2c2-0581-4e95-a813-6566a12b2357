<template>
  <div class="tab-container">
    <!-- Tab 切换背景 -->
    <div
      class="tab-background"
      :style="getImage(isHelpTab ? homeConfig.imgList.isHelpBg : homeConfig.imgList.assistBg)"
    >
      <!-- Tab 标题区域 - 点击切换 -->
      <div class="tab-header" @click="toggleTab">
        <div class="tab-title-left" :class="{ active: !isHelpTab }">
<!--          <slot name="leftTitle">助力我的好友</slot>-->
        </div>
        <div class="tab-title-right" :class="{ active: isHelpTab }">
<!--          <slot name="rightTitle">我助力的好友</slot>-->
        </div>
      </div>

      <!-- Tab 内容区域 -->
      <div class="tab-content">
        <div v-if="!isHelpTab" class="assist-content">
          <!-- 助力我的好友内容 -->
          <slot name="assistContent">
            <!-- 有数据时显示列表 -->
            <div v-if="assistList && assistList.length > 0" class="data-list">
              <div
                v-for="(item, index) in assistList"
                :key="index"
                class="list-item"
              >
                <div class="user-info">
                  <span class="user-phone">{{ item.userPhone || `用户${item.userId}****${item.userId}` }}</span>
                  <span class="bargain-amount">砍价 {{ item.amount }}元</span>
                </div>
                <div class="action-btn" :class="item.status">
                  {{ getActionText(item.status, false) }}
                </div>
              </div>
            </div>
            <!-- 无数据时显示提示 -->
            <div v-else class="content-placeholder">
              暂无助力成功的好友 快去邀请吧
            </div>
          </slot>
        </div>
        <div v-else class="help-content">
          <!-- 我助力的好友内容 -->
          <slot name="helpContent">
            <!-- 有数据时显示列表 -->
            <div v-if="helpList && helpList.length > 0" class="data-list">
              <div
                v-for="(item, index) in helpList"
                :key="index"
                class="list-item"
              >
                <div class="user-info">
                  <span class="user-phone">{{ item.userPhone || `用户${item.userId}****${item.userId}` }}</span>
                  <span class="bargain-amount">砍价 {{ item.amount }}元</span>
                </div>
                <div class="action-btn" :class="item.status">
                  {{ getActionText(item.status, true) }}
                </div>
              </div>
            </div>
            <!-- 无数据时显示提示 -->
            <div v-else class="content-placeholder">
              暂无我助力成功的好友
            </div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { getImage } from '@/utils/utils';

@Component({
  methods: { getImage }
})
export default class tabContainer extends Vue {

  /**
   * 配置
   */
  @Prop() homeConfig!: any;

  /**
   * 默认选中的 Tab (可选)
   * false: 助力我的好友 (默认)
   * true: 我助力的好友
   */
  @Prop() defaultTab!: boolean;

  /**
   * 助力我的好友数据列表
   */
  @Prop({ type: Array, default: () => [] }) assistList!: any[];

  /**
   * 我助力的好友数据列表
   */
  @Prop({ type: Array, default: () => [] }) helpList!: any[];

  /**
   * Tab 切换状态
   */
  isHelpTab: boolean = false;

  /**
   * 组件创建时初始化状态
   */
  created(): void {
    this.isHelpTab = this.defaultTab;
  }

  /**
   * 切换 Tab 页状态
   */
  toggleTab(): void {
    this.isHelpTab = !this.isHelpTab;
    // 触发事件，通知父组件状态变化
    this.$emit('tab-change', this.isHelpTab);
  }

  /**
   * 获取操作按钮文本
   * @param status 状态：'active' | 'completed' | 'expired'
   * @param isHelpTab 是否为"我助力的好友"Tab
   */
  getActionText(status: string, isHelpTab: boolean): string {
    if (isHelpTab) {
      // 我助力的好友Tab
      switch (status) {
        case 'completed':
          return '已帮砍';
        case 'active':
          return '翻倍';
        case 'expired':
          return '已帮砍';
        default:
          return '已帮砍';
      }
    } else {
      // 助力我的好友Tab
      switch (status) {
        case 'completed':
          return '已帮砍';
        case 'active':
          return '翻倍';
        case 'expired':
          return '已帮砍';
        default:
          return '翻倍';
      }
    }
  }
}
</script>

<style scoped lang="less">
// Tab 切换样式
.tab-container {
  .tab-background {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    transition: background-image 0.3s ease-in-out;

    .tab-header {
      display: flex;
      position: relative;
      cursor: pointer;

      .tab-title-left,
      .tab-title-right {
        flex: 1;
        padding: 1.1rem 0 0 0;
        //background: #666;
        transition: all 0.3s ease-in-out;
      }
    }

    .tab-content {
      padding-top: 0.1rem;
      min-height: 4.6rem;
      overflow-x: hidden;
      scrollbar-width: none; /* 对于Firefox */

      .content-placeholder {
        text-align: center;
        color: #999;
        font-size: 0.24rem;
        padding: 0.5rem 0;
      }

      .assist-content,
      .help-content {
        animation: fadeIn 0.3s ease-in-out;
      }

      // 数据列表样式
      .data-list {
        padding: 0.2rem 0.3rem;

        .list-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 0.24rem;
          padding: 0.32rem 0.4rem;
          margin-bottom: 0.2rem;
          box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);

          &:last-child {
            margin-bottom: 0;
          }

          .user-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 0.2rem;

            .user-phone {
              font-size: 0.28rem;
              color: #333;
              font-weight: 500;
            }

            .bargain-amount {
              font-size: 0.24rem;
              color: #FF001F;
              font-weight: 600;

              &::before {
                content: '⚡';
                margin-right: 0.05rem;
              }
            }
          }

          .action-btn {
            padding: 0.16rem 0.32rem;
            border-radius: 0.2rem;
            font-size: 0.24rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;

            // 翻倍按钮样式
            &.active {
              background: linear-gradient(135deg, #FF6B9D, #FF1744);
              color: white;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 0.04rem 0.12rem rgba(255, 23, 68, 0.3);
              }
            }

            // 已帮砍按钮样式
            &.completed {
              background: #F5F5F5;
              color: #999;
              cursor: default;
            }

            // 已过期按钮样式
            &.expired {
              background: #F5F5F5;
              color: #999;
              cursor: default;
            }
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
