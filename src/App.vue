<template>
  <div class="app-root">
    <loading v-if="showLoading && $versionNum" />
    <router-view />
    <err-modal ref="tipsDialog"></err-modal>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import { globalConfig, loadCss } from '@/utils/config';
import { userService } from '@/service/api';
import EventBus from '@/utils/evnetBus';
import { Dialog, Toast } from 'vant';
import Loading from '@/components/loading/Loading.vue';
import ErrModal from '@/components/modal/errModal.vue';
import { Mutation } from 'vuex-class';

@Component({
  components: { ErrModal, Loading }
})
export default class extends Vue {
  $configData: any;

  showLoading = true;

  // 温馨提示弹窗
  @Ref('tipsDialog') readonly tipsDialog!: any;

  @Mutation('setTokenData') setTokenData: any;

  created(): void {
    console.log('this.$versionNum', this.$versionNum);
    console.log('microUtils:', this.$microUtils);
    // 发布到国网环境需要这段代码
    EventBus.$on('showBounced', () => this.showBounced());

    // 监听全局 loading 清理事件
    EventBus.$on('clearAllLoading', () => {
      this.showLoading = false;
      console.log('Global loading cleared in App.vue');
    });

    this.$microUtils.customEventBus.$add('getUserInfo', async (res: any) => {
      console.log(
          '执行订阅。。。',
          res,
          this.$microUtils.getToken('getUserInfo')
      );
      if (res.data) {
        console.log('🚀 ~ file:App method: line:38 -----', res.data);
        const userinfo = res.data?.bizrt?.userInfo;
        if (userinfo) {
          this.$microUtils.setToken('mobile', userinfo?.mobile || '');
          this.$microUtils.setToken('user_id', userinfo.userId);
        }
      }
      await this.userAuthToken();
      // await this.getSgToken(res.data)
    });

    this.$microUtils.customEventBus.$add('tokenCallback', () => {
      console.log('🚀 ~ file:App method: line:41 -----', this.$microUtils.getToken('Token'));
      // this.infoSearch();
    });
    this.$microUtils.initToken();

    this.testIndex();  //todo 本地调试需要
    console.log('111----------', this.$microUtils.getToken('Token'));
  }

  // 在组件销毁前清除事件监听
  private beforeDestroy(): void {
    // EventBus.$off('showBounced');
  }

  //展示token失效弹框
  private showBounced() {
    // 弹窗展示逻辑
    if (this.tipsDialog) {
      let errObj = this.$configData.homePage.errDialogConfig.find(
        (it: any) => it.flag === 'token'
      );
      this.tipsDialog.open(errObj); // 显示/隐藏
    }
  }

  private async userAuthToken() {
    //延迟加载CSS;
    if (this.$versionNum) {
      setTimeout(() => {
        this.$versionNum &&
        loadCss(this.$versionNum, this.$configData.pageNameConfig);
        if (this.$configData.protectFlag) this.showLoading = false;
      }, 500);
    }

    //切换本地调 apiMethods ;云端调用  userService
    const [res, err] = await userService.getAuthToken({
      uid:
          this.$microUtils.getToken('user_id') || globalConfig.httpConfig.userId
    });
    this.showLoading = false;
    if (err) {
      const { rtn_msg } = err;
      Toast(rtn_msg || '当前人数参与太多,请稍后再试！');
      return this.$router.replace({ name: 'cover' });
    }
    if (!res) return;
    const { authToken } = res;
    this.$microUtils.setToken('authToken', authToken);
    console.log('🚀 ~ file:App method:userAuthToken line:119 -----', authToken);
    this.setTokenData({ sgccToken: authToken });
    sessionStorage.setItem('init', 'true');
    // 改变路由路径 用于首页监听
    await this.$router.replace({ name: 'cover' });
  }

  testIndex() {
    this.showLoading = false;
    console.log(
      '🚀 ~ file:App method:testIndex line:61 -----',
      this.$versionNum
    );
    if (this.$versionNum) {
      setTimeout(() => {
        loadCss(this.$versionNum, this.$configData.pageNameConfig);
        this.showLoading = false;
      }, 500);
    }
    sessionStorage.setItem('init', 'true');
    // 改变路由路径 用于首页监听
    this.$router.replace({ name: 'home', query: { init: 'true' } });
  }

  // 展示弹框
  showDialog() {
    Dialog.alert({
      title: '温馨提示',
      message: `${this.$configData.commonConfig.tips}`,
      showConfirmButton: false
    });
  }
}
</script>

<style lang="less">
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
.app-root {
  min-height: 100vh;
  //background: #f5f5f5;
}
</style>
