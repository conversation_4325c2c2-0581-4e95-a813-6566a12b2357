// vue2自定义全局指令
import Vue from 'vue'

Vue.directive('loadImg', {
  inserted(el, binding) {
    const img = new Image()
    img.src = binding.value
    img.onload = () => {
      el.style.backgroundImage = `url(${binding.value})`
      el.style.width = `${img.width / 100}rem`
      el.style.height = `${img.height / 100}rem`
      el.style.backgroundSize = '100% 100%'
      el.style.backgroundRepeat = 'no-repeat'
      el.style.backgroundPosition = '0 0'
    }
  },
  update(el, binding) {
    const img = new Image()
    img.src = binding.value
    img.onload = () => {
      el.style.backgroundImage = `url(${binding.value})`
      el.style.width = `${img.width / 100}rem`
      el.style.height = `${img.height / 100}rem`
      el.style.backgroundSize = '100% 100%'
      el.style.backgroundRepeat = 'no-repeat'
      el.style.backgroundPosition = '0 0'
    }
  },
})
