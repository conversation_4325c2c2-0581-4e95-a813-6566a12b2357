<script lang="ts">
import { Vue, Component, Prop, Ref } from 'vue-property-decorator';
import Modal from '@/components/modal/Modal.vue';
import { errImg } from '@/utils/constant';
import { getAppSource, getLocation, isiOS } from '@/utils/utils';
import store from '@/store';

@Component({
  computed: {},
  components: { Modal }
})
export default class errModal extends Vue {
  @Prop() pageTab: any;
  isVerifyTime = false;
  describe = '';
  @Ref('modalRef') modalRef: any;

  errObj: any = {}; // 提示弹窗配置内容

  showBtn: any = true;

  open(obj: any) {
    this.errObj = obj;
    this.describe = obj.tips;
    this.showBtn = obj.showBtn;
    this.modalRef.show = true;
  }

  handleBtn() {
    this.modalRef.show = false;
    let flag = this.errObj.flag;
    if (flag === 'unBind') {
      // 绑定北京户号，点击“去绑定”，去国网户号绑定页
      this.bindFun();
    } else if (flag === 'location') {
      // 允许授权地理位置，点击“确定”，去授权
      this.positionClick();
    } else if (
      ['no-bj-location', 'token', 'noStart', 'hasEnd'].includes(flag)
    ) {
      // token超时，点击“确定” ，退出活动
      // 非北京地区，点击“确定”，退出活动
      // flag == "token" || flag == 'noStart' || flag == 'hasEnd' ? this.exitFun() : this.modalRef.show = false;
      this.exitFun();
    } else {
      this.modalRef.show = false;
    }
  }

  // 高德定位
  positionClick() {
    this.$toast.loading({
      message: '位置正在搜索中...',
      forbidClick: true,
      duration: 0
    });
    getLocation(this.checkArea, this.errDialog);
    // todo：如果用户“不允许”授权地理位置，则退出活动
  }

  private async checkArea(citySearchResult: any) {
    this.$toast.clear();
    store.commit('SET_LOCATION', citySearchResult.position);
    this.$emit('locationJudge', citySearchResult.position);
  }

  // 定位失败回调函数
  private errDialog() {
    this.$toast.clear();
    if (
      isiOS() &&
      getAppSource() &&
      parseInt(<any>localStorage.getItem('bjFanIosWxCount')) >= 2
    ) {
      // 其他定位错误
      let errObj = this.$configData.homePage.errDialogConfig.find(
        (it: any) => it.flag === 'other-location-warn'
      );
      this.errObj = errObj;
      this.describe = errObj.tips;
    } else {
      // 非北京地区
      let errObj = this.$configData.homePage.errDialogConfig.find(
        (it: any) => it.flag === 'no-bj-location'
      );
      this.errObj = errObj;
      this.describe = errObj.tips;
    }
    this.showBtn = true;
    this.modalRef.show = true;
  }

  bindFun() {
    //户号管理编码（A40050000）
    this.$publics
      .JSBridgeMethod('jumpToMenu', { data: { menuId: 'A40050000' } })
      .then((res: any) => {
        console.log('jumpToMenu 反参' + res);
        console.log('jumpToMenu 反参' + JSON.stringify(res));
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  // 退出活动
  exitFun() {
    WSGWBridge.callNativeFunction('close', {}, function callback(
      response: any
    ) {
      console.log('返回', response);
    });
  }
}
</script>

<template>
  <modal
    :bg-obj="{
      background: showBtn
        ? $configData.homePage.errImg.areaBg
        : $configData.homePage.errImg.commonBg
    }"
    ref="modalRef"
    class="modal-dialog"
    :show-close-button="false"
  >
    <div class="common_info flex_column" v-if="!showBtn">
      <div class="err_text  font_28 text_align_center" v-html="describe"></div>
    </div>
    <div class="err_info flex_column" v-else>
      <div class="area_text  font_28 text_align_center" v-html="describe"></div>
    </div>

    <div
      class="btn flex_column"
      :class="[showBtn ? 'area_btn' : '']"
      v-if="errObj.btnTxt"
      @click="handleBtn()"
    >
      <div class="tips  font_32">{{ errObj.btnTxt }}</div>
    </div>
  </modal>
</template>

<style scoped lang="less">
.modal-dialog {
  .err_text {
    position: absolute;
    top: 2.24rem;
    //padding: 0 0.1rem;
    color: #383838;
  }

  .area_text {
    position: absolute;
    top: 2.24rem;
    //padding: 0 0.1rem;
    color: #383838;
  }

  .btn {
    position: absolute;
    top: 3.26rem;
    left: 1.71rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20241103133102007100042046500300_w300_h120.png')
      no-repeat;
    background-size: 100%;
    width: 3rem;
    height: 1.2rem;

    .tips {
      color: #fefefe;
    }
  }

  .area_btn {
    top: 4.06rem;
  }
}
</style>
