import { Vue } from 'vue-property-decorator';
import EventBus from '@/utils/evnetBus';

export class MicroUtils extends Vue {
  // 配置信息
  private configInfo: any;
  // 数据中心
  private callBacks: any = {};

  public customEventBus = {
    // 添加
    $add: (name: string, fun: string) => {
      // 多次订阅可执行对应的方法，使用数组存储
      this.callBacks[name] = this.callBacks[name] || [];
      // 把订阅方法名对应的方法存入数组中
      this.callBacks[name].push(fun);
    },
    // 发布
    $emit: (name: string | number, ...args: any) => {
      if (this.callBacks[name] && this.callBacks[name].length > 0) {
        // 存在订阅的属性名，则遍历数组里所有的方法
        //@ts-ignore
        this.callBacks[name].forEach(cb => cb(...args));
      }
    },
    $off: (name: string | number, callback?: any) => {
      if (this.callBacks[name] && this.callBacks[name].length > 0) {
        if (callback) {
          const index = this.callBacks[name].indexOf(callback);
          this.callBacks[name].splice(index, 1);
        } else {
          this.callBacks[name].length = 0;
        }
      }
    }
  };
  private accessToken = '';
  private inputValue = [
    {
      leftText: 'URL',
      placeholder: '接口路径',
      value: '',
      status: false,
      regExp: '0',
      name: ''
    },
    {
      leftText: 'Enter the reference',
      placeholder: '入参',
      value: '',
      status: false,
      regExp: '0',
      name: ''
    }
  ];
  private activityName = '';

  constructor(isConfigInfo: object, tokenName: string) {
    super();
    this.configInfo = isConfigInfo;
    this.activityName = tokenName;
  }

  public initToken() {
    this.getOpenAuth();
    console.log('initToken', this.configInfo);
    //todo 本地测试用的
    // this.$microUtils.customEventBus.$emit("tokenCallback");
  }
  // 获取Token
  public getToken(storageKey: string) {
    // eslint-disable-next-line no-useless-catch
    try {
      return localStorage.getItem(`${this.activityName}_${storageKey}`);
    } catch (e) {
      throw e;
    }
  }

  public setToken(storageKey: string, token: string) {
    window.localStorage.setItem(`${this.activityName}_${storageKey}`, token);
  }

  // 获取开始时间
  public getTimeStamp() {
    return this.getToken('StartTime');
  }

   /**
     * 获取 accessToken
     *
     */
   public getAccessToken() {
     return this.accessToken;
   }

  private getOpenAuth() {
    console.log('第一步骤中获取URL');
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {} // 业务入参
    };
    this.$publics
      .JSBridgeMethod('getOpenAuth', param)
      .then((res: any) => {
        console.log('getOpenAuth 反参' + res);
        console.log('getOpenAuth 反参' + JSON.stringify(res));
        const openUrl = this.getRequest('code', res.data.openUrl);
        this.getOpenAuthToken(openUrl);
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  /**
   * 获取地址栏中的参数
   * @param name  参数名
   * @returns {string} 返回参数值
   */
  private getRequest(name: any, url: any) {
    const result = url.match(new RegExp('[?&]' + name + '=([^&]+)', 'i'));
    if (result == null || result.length < 1) {
      return '';
    }
    // console.log('获取地址栏中的参数'+result[1])
    console.log('getRequest + 获取地址栏中的参数' + result[1]);
    return result[1];
  }

  private getOpenAuthToken(openUrl: any) {
    console.log('第一步骤中获取accessToken');
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {
        url: '/oauth2/oauth/token',
        data: {
          client_id: this.configInfo.CLIENT_ID,
          redirect_uri: this.configInfo.REDIRECT_URI,
          client_secret: this.configInfo.CLIENT_SECRET,
          code: openUrl
        }
      } // 业务入参
    };
    this.$publics
      .JSBridgeMethod('getOpenAuthToken', param)
      .then((res: any) => {
        console.log('getOpenAuthToken 反参' + res);
        console.log('getOpenAuthToken 反参' + JSON.stringify(res));
        this.accessToken = res.data.access_token;
        this.tokenTransformation();
        /*常州地区不需要*/
        // this.jumpToMenu()
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  /*---------------*/

  // 第二步：获取用户token
  public tokenTransformation() {
    console.log('第二步骤中获取用户token');
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {
        url: '/oauth2/outer/c01/tokenTransformation',
        type: '01',
        accessToken: this.accessToken || '--', // "asdweefwere121321e21e321",
        data: {
          uscInfo: {
            member: '2202',
            tenant: 'state_grid',
            deviceId: '',
            deviceIp: ''
          },
          quInfo: {
            appCode: this.configInfo.APP_CODE
          },
          client_id: this.configInfo.CLIENT_ID,
          client_secret: this.configInfo.CLIENT_SECRET
        }
      } // 业务入参
    };
    this.$publics
      .JSBridgeMethod('NLRequest', param)
      .then((res: any) => {
        console.log('tokenTransformation 反参' + res);
        console.log('tokenTransformation 反参' + JSON.stringify(res));
        // this.accessToken = res.data.access_token
        const data = res.data;
        const token = data.bizrt.token;
        const startTime = data.srvrt.timeStamp;
        console.log('取token' + token);
        this.setToken('Token', token);
        this.setToken('StartTime', startTime);
        this.$microUtils.customEventBus.$emit('tokenCallback');
        this.getUserInfo()
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  /*---------------*/

  // 第三步：用户授权展示
  public async jumpToMenu() {
    console.log('第三步骤中获取用户授权展示');
    await this.sendNotice();
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {
        key: 'authorityPersonalInfo',
        value: {
          grantType: 'userPhone',
          appCode: this.configInfo.CLIENT_ID,
          accessToken: this.accessToken
        }
      } // 业务入参s
    };
    console.log('setTempCache 入参' + param);
    this.$publics
      .JSBridgeMethod('setTempCache', param)
      .then((res: any) => {
        console.log('setTempCache 反参' + res);
        console.log('setTempCache 反参' + JSON.stringify(res));
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
    setTimeout(() => {
      console.log('jumpToMenu 入参' + { menuId: 'R10000043' });
      this.$publics
        .JSBridgeMethod('jumpToMenu', { data: { menuId: 'R10000043' } })
        .then((res: any) => {
          console.log('jumpToMenu 反参' + res);
          console.log('jumpToMenu 反参' + JSON.stringify(res));
        })
        .catch((res: any) => {
          console.error(res + '异常');
        });
    }, 1000);
  }

  public sendNotice() {
    console.log('sendNotice方法');
    const param = {
      notice: [
        {
          menuId: 'R10000043', // 哪个微应用
          key: this.configInfo.CLIENT_ID // 监听哪个key
        }
      ],
      appId: this.configInfo.CLIENT_ID
    };
    console.log('registerNotice' + param);
    this.$publics
      .JSBridgeMethod('registerNotice', { data: param })
      .then((res: any) => {
        console.log('registerNotice 反参' + res);
        console.log('registerNotice 反参' + JSON.stringify(res));
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  // 其它：通用访问能力开放平台业务接口请求
  public NLRequest() {
    console.log('通用访问能力开放平台业务接口请求');
    if (this.inputValue[0].value == '') {
      console.log('请输入接口路径');
      return;
    }
    if (this.inputValue[1].value == '') {
      console.log('请输入接口入参');
      return;
    }
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {
        url: this.inputValue[1].value, // "/osg-open-uc0001/member/c8/f63"
        notShowLoading: 0,
        type: '01',
        accessToken: this.accessToken || '--', // "asdweefwere121321e21e321",
        data: JSON.parse(this.inputValue[1].value)
      }
    };

    this.$publics
      .JSBridgeMethod('NLRequest', param)
      .then((res: any) => {
        console.log('NLRequest 反参' + res);
        console.log('NLRequest 反参' + JSON.stringify(res));
        // this.accessToken = res.data.accessToken
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }

  /**
   *
   * 获取用户信息
   */

  public async getUserInfo() {
    console.log('获取用户信息');
    const token = this.getToken('Token');
    if (!token) return EventBus.$emit('showBounced');
    const par = {
      uscInfo: {
        token: token,
        tenant: 'state_grid',
        member: this.configInfo.APP_CODE,
        devciceId: undefined,
        devciceIp: undefined
      },
      quInfo: {
        addressProvince: undefined,
        addressCity: undefined,
        addressRegion: undefined,
        optSys: undefined
      },
      token: token
    };
    this.inputValue[0].name = 'getUserInfo';
    this.inputValue[0].value = '/osg-open-uc0001/member/c8/f56';
    this.inputValue[1].value = JSON.stringify(par);
    await this.commonRequest();
  }

  /**
   * 通用请求
   *
   */
  public async commonRequest() {
    const param = {
      url: '', // 接口路径
      AnalogData: '', // 本地文件地址/名称
      AnalogDataname: '获取xxx数据', // 方法或接口描述
      data: {
        url: this.inputValue[0].value, // "/osg-open-uc0001/member/c8/f63"
        notShowLoading: 0,
        type: '01',
        accessToken: this.accessToken || '--', // "asdweefwere121321e21e321",
        data: JSON.parse(this.inputValue[1].value)
      }
    };
    this.$publics
      .JSBridgeMethod('NLRequest', param)
      .then((res: any) => {
        console.log('NLRequest 反参', param, res);
        console.log('NLRequest 反参' + JSON.stringify(res));
        this.setToken(this.inputValue[0].name, JSON.stringify(res))
        this.$microUtils.customEventBus.$emit(this.inputValue[0].name, res);
      })
      .catch((res: any) => {
        console.error(res + '异常');
      });
  }
}
