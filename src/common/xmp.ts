// 异常类型(type字段)
export const GATEWAY_TYPE = "gateway"; // 网关日志
export const JSAPI_TYPE = "jsApi"; // jsApi日志

const eventNames = ["AppClick", "BizError"];

export function myTrackEvent(
  eventName: "AppClick" | "BizError",
  params: object
) {
  const findEvent = eventNames.find((item) => item === eventName);
  if (!findEvent) return;
  // @ts-ignore
  return new Promise<void>((resolve) => {
    // @ts-ignore
    if (window._bzt && window._bzt.trackEvent) {
      // @ts-ignore
      window._bzt.trackEvent(findEvent, {
        // 事件参数
        ...params
      });
    }
    resolve();
  });
}

// 元素点击事件埋点
export function trackAppClickEvent(name: string, params?: object) {
  myTrackEvent("AppClick", {
    trigger_type: "click",
    element_name: name,
    ...params
  });
}
