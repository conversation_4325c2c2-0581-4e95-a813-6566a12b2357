import { Dialog, Toast } from 'vant'

export type ErrorTipParams = {
  showType?: 'toast' | 'alert' | 'confirm'
  title?: string
  message: string
  okBtnText?: string
  oKBtnHandle?: () => void
  cancelBtnText?: string
  cancelBtnHandle?: () => void
  messageAlign?: 'left' | 'right' | 'center'
}

/**
 * 错误信息展示通用方法（toast，alert, confirm）
 * @param {*} cfg
 */
export function showErrorMsg(cfg?: ErrorTipParams) {
  if (!cfg) return
  const {
    showType,
    title,
    message,
    okBtnText = '确定',
    oKBtnHandle,
    cancelBtnText = '取消',
    cancelBtnHandle,
    messageAlign,
  } = cfg
  if (showType === 'confirm') {
    Dialog.confirm({
      title,
      message,
      confirmButtonText: okBtnText,
      cancelButtonText: cancelBtnText,
      messageAlign,
    })
      .then(() => {
        // on confirm
        if (oKBtnHandle && typeof oKBtnHandle === 'function') {
          oKBtnHandle()
        }
      })
      .catch(() => {
        // on cancel
        if (cancelBtnHandle && typeof cancelBtnHandle === 'function') {
          cancelBtnHandle()
        }
      })
  } else if (showType === 'alert') {
    Dialog.alert({
      title,
      message,
      confirmButtonText: okBtnText,
    }).then(() => {
      // on confirm
      if (oKBtnHandle && typeof oKBtnHandle === 'function') {
        oKBtnHandle()
      }
    })
  } else {
    Toast(message)
  }
}
