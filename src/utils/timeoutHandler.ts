import { Toast, Dialog } from 'vant';
import EventBus from '@/utils/evnetBus';
import { RemoteApiServiceSingleton } from '@/service/remoteApiService';

/**
 * 全局超时处理工具类
 * 用于统一处理请求超时、清理 loading 状态、显示用户友好的错误提示
 */
export class TimeoutHandler {
  private static instance: TimeoutHandler;
  private isHandlingTimeout = false;

  private constructor() {
    this.setupGlobalErrorHandlers();
  }

  public static getInstance(): TimeoutHandler {
    if (!TimeoutHandler.instance) {
      TimeoutHandler.instance = new TimeoutHandler();
    }
    return TimeoutHandler.instance;
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 监听全局清理 loading 事件
    EventBus.$on('clearAllLoading', () => {
      this.clearAllLoadingStates();
    });

    // 监听页面可见性变化，当页面重新可见时清理可能残留的 loading
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.isHandlingTimeout) {
          this.clearAllLoadingStates();
          this.isHandlingTimeout = false;
        }
      });
    }
  }

  /**
   * 处理超时错误
   * @param error 错误对象
   * @param options 处理选项
   */
  public handleTimeout(error: any, options: {
    showRetryDialog?: boolean;
    retryCallback?: () => void;
    customMessage?: string;
  } = {}): void {
    if (this.isHandlingTimeout) {
      return; // 防止重复处理
    }

    this.isHandlingTimeout = true;
    console.error('Handling timeout error:', error);

    // 立即清理所有 loading 状态
    this.clearAllLoadingStates();

    // 显示用户友好的错误提示
    const message = options.customMessage || '请求超时，请检查网络连接';
    
    if (options.showRetryDialog && options.retryCallback) {
      this.showRetryDialog(message, options.retryCallback);
    } else {
      this.showTimeoutToast(message);
    }

    // 延迟重置处理状态
    setTimeout(() => {
      this.isHandlingTimeout = false;
    }, 1000);
  }

  /**
   * 清理所有 loading 状态
   */
  public clearAllLoadingStates(): void {
    try {
      // 清理 Toast loading
      Toast.clear();
      
      // 清理可能存在的 Dialog loading
      Dialog.close();
      
      // 发送全局事件通知所有组件清理 loading 状态
      EventBus.$emit('clearAllLoading');
      
      // 清理远程 API 服务的 loading 状态
      RemoteApiServiceSingleton.clearAllLoading();
      
      console.log('All loading states cleared');
    } catch (error) {
      console.error('Error clearing loading states:', error);
    }
  }

  /**
   * 显示超时提示 Toast
   */
  private showTimeoutToast(message: string): void {
    Toast({
      message,
      type: 'fail',
      duration: 3000,
      forbidClick: false,
    });
  }

  /**
   * 显示重试对话框
   */
  private showRetryDialog(message: string, retryCallback: () => void): void {
    Dialog.confirm({
      title: '网络超时',
      message: `${message}\n是否重试？`,
      confirmButtonText: '重试',
      cancelButtonText: '取消',
      confirmButtonColor: '#1989fa',
    }).then(() => {
      // 用户点击重试
      retryCallback();
    }).catch(() => {
      // 用户点击取消，不做任何操作
      console.log('User cancelled retry');
    });
  }

  /**
   * 检查是否为超时错误
   */
  public static isTimeoutError(error: any): boolean {
    return (
      error?.code === 'ECONNABORTED' ||
      error?.message?.includes('timeout') ||
      error?.message?.includes('Network Error') ||
      error?.isTimeout === true ||
      error?.rtnFlag === 'timeout_error' ||
      (error?.response?.status === undefined && error?.request)
    );
  }

  /**
   * 创建带超时处理的 Promise
   */
  public static createTimeoutPromise<T>(
    promise: Promise<T>, 
    timeout: number = 30000,
    timeoutMessage?: string
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(timeoutMessage || `Operation timeout after ${timeout}ms`));
      }, timeout);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * 销毁实例（用于测试或特殊情况）
   */
  public static destroy(): void {
    if (TimeoutHandler.instance) {
      EventBus.$off('clearAllLoading');
      TimeoutHandler.instance = null as any;
    }
  }
}

// 导出单例实例
export const timeoutHandler = TimeoutHandler.getInstance();

// 导出便捷方法
export const handleTimeout = (error: any, options?: Parameters<typeof timeoutHandler.handleTimeout>[1]) => {
  timeoutHandler.handleTimeout(error, options);
};

export const clearAllLoading = () => {
  timeoutHandler.clearAllLoadingStates();
};

export const isTimeoutError = TimeoutHandler.isTimeoutError;

export const createTimeoutPromise = TimeoutHandler.createTimeoutPromise;
