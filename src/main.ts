import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './assets/style/index.less';

Vue.config.productionTip = false;

import publics from '@/common/uap.public';
import { MicroUtils } from '@/common/microUtils';
import { getConfigData, loadJsFun } from '@/utils/config';

import Vconsole from 'vconsole';
import { createFingerprint } from '@/common/fingerprint';
import 'vant/lib/index.css';

import {
  Toast,
  Loading,
  Button,
  CountDown,
  Icon,
  Field,
  Swipe,
  SwipeItem,
  Picker,
  Progress,
  Popup,
  List,
  PullRefresh,
  Checkbox,
  Dialog
} from 'vant';
import { Image as VanImage } from 'vant';
import {getImgSize} from "@/utils/common";

Vue.use(Toast).use(Loading).use(VanImage).use(Button).use(Icon).use(Field).use(Swipe).use(SwipeItem).use(Picker).use(Popup).use(List).use(PullRefresh).use(CountDown).use(Checkbox).use(Progress);

// 设置toast默认配置
Toast.setDefaultOptions({
  forbidClick: true, // 背景不可点击
  overlay: true // 显示背景遮罩层
});

Vue.prototype.$publics = publics;

// 全局图片处理函数
Vue.prototype.$getImgSize = getImgSize

getConfigData().then((res: any) => {
  const configData = res.data;
  Vue.prototype.$configData = configData;
  console.log("🚀 ~ file:main method: line:37 -----", configData)
// 挂维护弹窗
  if (configData.maintainConfig?.flag) {
    Dialog.alert({
      message: configData.maintainConfig.text,
      confirmButtonColor: "#108ee9"
    }).then(() => {
      WSGWBridge.callNativeFunction("close", {}, function callback(response :any) {
        console.log("返回", response);
      });
    });
    return;
  }
  const configInfo = {
    // appkey APPCODE
    CLIENT_ID: configData.basicConfig.CLIENT_ID,
    // 应用回调页面地址
    REDIRECT_URI: configData.basicConfig.REDIRECT_URI,
    // appSecret
    CLIENT_SECRET: configData.basicConfig.CLIENT_SECRET,
    // QUINFO 下appCode 渠道编码
    APP_CODE: configData.basicConfig.APP_CODE
  };
  createFingerprint().then((result: any) => {
    console.log('fingerPrint-----', result);
    Vue.prototype.$microUtils = new MicroUtils(configInfo, 'bjFanSeek');
    // debugger;
    //开发配置信息
    const devConfig = configData.devConfig;
    //项目配置信息
    const projectConfig = configData.projectConfig;
    //控制台白名单
    const consoleWhiteList = devConfig.consoleWhiteList;
    const index = consoleWhiteList.findIndex(
      (it: { consoleSign: any }) => it.consoleSign === result
    );
    if (index > -1) {
      // 测试和开发打开，生产不能打开
      const vConsole = new Vconsole();
      //@ts-ignore
      Vue.use(vConsole);
    }
    //历史版本
    const versionHistory = projectConfig.versionHistory;
    //当前版本
    const versionNumberInfo = projectConfig.versionNumberInfo;

    if (devConfig.checkVersion) {
      const versionWhiteList = devConfig.versionWhiteList;
      const versionIndex = versionWhiteList.findIndex(
        (it: { versionSign: any }) => it.versionSign === result
      );
      const whiteVerNumInfo = versionWhiteList[versionIndex]?.versionNumberInfo;
      //todo 版本白名单
      Vue.prototype.$versionNum =
        versionIndex > -1
          ? whiteVerNumInfo
          : projectConfig.checkRollback
            ? versionHistory
            : versionNumberInfo;
    } else {
      //是否开启回滚，开启
      Vue.prototype.$versionNum = projectConfig.checkRollback
        ? versionHistory
        : versionNumberInfo;
    }
    if (!Vue.prototype.$versionNum) {
      new Vue({
        router,
        store,
        render: (h) => h(App),
      }).$mount('#app');
      return;
    }
    loadJsFun(Vue.prototype.$versionNum, configData.pageNameConfig).then((r: any) => {
      console.log(r);
      new Vue({
        router,
        store,
        render: (h) => h(App),
      }).$mount('#app');
    });
  });
});
