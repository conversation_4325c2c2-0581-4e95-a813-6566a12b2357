<template>
  <div class="tab-container">
    <!-- Tab 切换背景 -->
    <div
      class="tab-background"
      :style="getImage(isHelpTab ? homeConfig.imgList.isHelpBg : homeConfig.imgList.assistBg)"
    >
      <!-- Tab 标题区域 - 点击切换 -->
      <div class="tab-header" @click="toggleTab">
        <div class="tab-title-left" :class="{ active: !isHelpTab }">
<!--          <slot name="leftTitle">助力我的好友</slot>-->
        </div>
        <div class="tab-title-right" :class="{ active: isHelpTab }">
<!--          <slot name="rightTitle">我助力的好友</slot>-->
        </div>
      </div>

      <!-- Tab 内容区域 -->
      <div class="tab-content">
        <div v-if="!isHelpTab" class="assist-content">
          <slot name="assistContent">
            <div class="content-placeholder" v-html="homeConfig.titleConfig.assistContent"></div>
          </slot>
        </div>
        <div v-else class="help-content">
          <slot name="helpContent">
            <div class="content-placeholder" v-html="homeConfig.titleConfig.helpContent"></div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { getImage } from '@/utils/utils';

@Component({
  methods: { getImage }
})
export default class tabContainer extends Vue {

  /**
   * 配置
   */
  @Prop() homeConfig!: any;

  /**
   * 默认选中的 Tab (可选)
   * false: 助力我的好友 (默认)
   * true: 我助力的好友
   */
  @Prop() defaultTab!: boolean;

  /**
   * Tab 切换状态
   */
  isHelpTab: boolean = false;

  /**
   * 组件创建时初始化状态
   */
  created(): void {
    this.isHelpTab = this.defaultTab;
  }

  /**
   * 切换 Tab 页状态
   */
  toggleTab(): void {
    this.isHelpTab = !this.isHelpTab;
    // 触发事件，通知父组件状态变化
    this.$emit('tab-change', this.isHelpTab);
  }
}
</script>

<style scoped lang="less">
// Tab 切换样式
.tab-container {
  .tab-background {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    transition: background-image 0.3s ease-in-out;

    .tab-header {
      display: flex;
      position: relative;
      cursor: pointer;

      .tab-title-left,
      .tab-title-right {
        flex: 1;
        padding: 1.1rem 0 0 0;
        //background: #666;
        transition: all 0.3s ease-in-out;
      }
    }

    .tab-content {
      padding-top: 0.1rem;
      min-height: 4.6rem;
      overflow-x: hidden;
      scrollbar-width: none; /* 对于Firefox */

      .content-placeholder {
        text-align: center;
        color: #999;
        font-size: 0.24rem;
        padding: 0.5rem 0;
      }

      .assist-content,
      .help-content {
        animation: fadeIn 0.3s ease-in-out;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
