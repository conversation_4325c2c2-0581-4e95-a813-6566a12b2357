/**
 * JSBridgeMethod 原声桥接方法
 * @param {*} MethodName 方法名称
 * @param {*} param       入参
 * @param     param.url             : 接口请求路径
 * @param     param.data            : 接口请求入参 业务参数
 * @param     param.AnalogData      : 模拟数据文件名称或路径
 * @param     param.AnalogDataname  : 方法名称
 * @returns
 */
export const JSBridgeMethod = (MethodName: any, param: any = {}) =>
  new Promise((resolve, reject) => {
    const platform = window.navigator.platform;
    console.log(platform + " JSBridge " + MethodName + JSON.stringify(param));
    try {
      // 浏览器环境
      if ((platform == "Win32" || platform == "MacIntel") && param.AnalogData) {
        // const data = require('$api/data/'+param.AnalogData+'.json').data
        resolve({});
      } else {
        // app环境
        console.log(
          "app环境下" +
            MethodName +
            " " +
            JSON.stringify(param) +
            " -" +
            MethodName +
            "CallBack"
        );

        WSGWBridge.callNativeFunction(
          MethodName,
          JSON.stringify(param.data),
          function callback(res: any) {
            console.log(res);
            console.log(param.AnalogDataname + " 反参 " + JSON.stringify(res));
            if (res && res.code == 1) {
              resolve(res);
            } else {
              reject(res);
              console.error(MethodName + "异常");
            }
          }
        );
      }
    } catch (err) {
      console.log("err环境下 ---" + err);
      console.error(err + "异常");
    }
  });

export default { JSBridgeMethod }; // 原生交互 和 全局方法 原声桥接方法交互
/*module.exports = {
  JSBridgeMethod, // 原声桥接方法交互
}*/
