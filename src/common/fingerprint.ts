// @ts-ignore
import Fingerprint2 from "fingerprintjs2";
// 创建浏览器指纹
export function createFingerprint() {
  const options = {
    excludes: {
      audio: true, // 不同浏览器，同一浏览器，不同设备
      webgl: true, // 不同浏览器影响
      screenResolution: true, // 同一浏览器，不同设备
      userAgent: true, // 不同浏览器，同一浏览器，不同设备
      colorDepth: true, // 不同浏览器影响
      deviceMemory: true, // 不同浏览器影响
      pixelRatio: true, // 不同浏览器，同一浏览器，不同设备
      availableScreenResolution: true, // 不同浏览器，同一浏览器，不同设备
      openDatabase: true, // 不同浏览器
      plugins: true, // 不同浏览器
      canvas: true, // 不同浏览器
      webglVendorAndRenderer: true, // 不同浏览器
      hasLiedOs: true, // 不同浏览器，同一浏览器，不同设备
      touchSupport: true, // 不同浏览器
      enumerateDevices: true, // 不同浏览器，同一浏览器，不同设备
      hardwareConcurrency: true, // 不同浏览器，同一浏览器，不同设备
      hasLiedBrowser: true // 不同浏览器
    }
  };
  // const fingerPrintData = "";
  return new Promise(function(resolve) {
    // 浏览器指纹
    Fingerprint2.get(options, (components: any[]) => {
      console.log("component", components);
      // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
      const values = components.map(component => component.value); // 配置的值的数组
      console.log("values", values);
      const murmur = Fingerprint2.x64hash128(values.join(""), 31); // 生成浏览器指纹
      localStorage.setItem("browserId", murmur); // 存储浏览器指纹，在项目中用于校验用户身份和埋点
      resolve(murmur);
    });
  });
}
