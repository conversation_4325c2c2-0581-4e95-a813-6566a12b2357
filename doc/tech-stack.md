# 项目技术栈基本信息文档

## 项目概述

**项目名称**: hebei-money-saving-act (河北省钱行动派)
**项目版本**: 0.0.1
**项目描述**: 河北省钱行动派活动页面 - 国网电力营销活动H5项目
**开发团队**: bangdao-fe
**运行环境**: 国网App内嵌H5页面 (WSGWBridge)

## 核心技术栈

### 前端框架
- **Vue.js**: ~2.6.11
  - 采用Vue 2.x版本，使用Options API和Class Component语法
  - 支持TypeScript开发
  - 使用vue-class-component和vue-property-decorator进行类组件开发

### 开发语言
- **TypeScript**: 4.3.5
  - 启用严格模式 (`"strict": true`)
  - 支持装饰器 (`"experimentalDecorators": true`)
  - 目标编译版本: ES5

### UI组件库
- **Vant**: ^2.12.31
  - 移动端Vue组件库
  - 按需引入组件：Toast、Loading、Button、CountDown、Icon、Field、Swipe、SwipeItem、Picker、Popup、List、PullRefresh、Checkbox、Dialog、Image

### 状态管理
- **Vuex**: ^3.0.1
  - 集中式状态管理
  - 配合vuex-class进行类组件状态绑定

### 路由管理
- **Vue Router**: ^3.0.3
  - Hash模式路由
  - 支持懒加载和代码分割

### 网络请求
- **Axios**: ^0.19.0
- **@bangdao/apollo**: ^1.1.0-alpha.18
  - 邦道自研HTTP请求封装库
  - 支持请求拦截、响应处理、错误处理
  - 支持SM2/SM3国密加解密

### 样式预处理
- **Less**: ^3.10.2
- **Less-loader**: ^5.0.0

### 构建工具
- **Vue CLI**: ~4.4.0
  - 基于Webpack的构建工具
  - 支持TypeScript、ESLint、Babel等插件

### 代码质量
- **ESLint**: @bangdao/eslint-config-vue ^1.0.5
- **Prettier**: ^1.19.1
- **Husky**: ^4.3.7 (Git hooks)
- **lint-staged**: ^10.5.3 (暂存文件检查)

### 工具库
- **lodash.clonedeep**: ^4.5.0 (深拷贝)
- **clipboard**: ^2.0.11 (剪贴板操作)
- **fingerprintjs2**: ^2.1.4 (浏览器指纹)
- **jsencrypt**: ^3.2.1 (RSA加密)
- **sm-crypto**: ^0.3.6 (国密SM2/SM3算法)
- **js-base64**: ^3.7.8 (Base64编解码)
- **jsonrepair**: ^3.8.0 (JSON修复)
- **lottie-web**: ^5.9.1 (动画库)
- **gsap**: ^3.12.5 (动画库)
- **swiper**: 5.2.0 (轮播组件)
- **vue-seamless-scroll**: ^1.1.23 (无缝滚动)
- **moment**: ^2.29.4 (日期处理)
- **uuid**: ^10.0.0 (UUID生成)

### 调试工具
- **vconsole**: ^3.14.6 (移动端调试控制台)
- **webpack-bundle-analyzer**: ^4.8.0 (打包分析)

### 邦道生态组件
- **@bangdao/common**: ^1.0.16 (通用工具库)
- **@bangdao/captcha-multi**: ^0.2.9 (多类型验证码组件)

## 项目架构模式

### 目录结构
```
src/
├── assets/          # 静态资源
├── common/          # 通用工具和配置
├── components/      # 全局组件
├── pages/           # 页面组件
├── service/         # API服务层
├── store.ts         # Vuex状态管理
├── router.ts        # 路由配置
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
└── main.ts          # 应用入口
```

### 设计理念

1. **组件化开发**: 采用Vue组件化思想，将UI拆分为可复用的组件
2. **类型安全**: 全面使用TypeScript，提供完整的类型定义
3. **模块化**: 按功能模块组织代码，便于维护和扩展
4. **响应式设计**: 适配移动端，使用rem单位和flexible布局
5. **性能优化**: 
   - 路由懒加载
   - 组件按需引入
   - 代码分割优化
   - 图片预加载

### 构建配置

#### 代码分割策略
```javascript
// vue.config.js 中的分包配置
splitChunks: {
  common: {
    name: 'business-common',
    test: /[\\/]src[\\/]common[\\/]/,
    priority: 8
  },
  utils: {
    name: 'business-utils', 
    test: /[\\/]src[\\/]utils[\\/]/,
    priority: 9
  },
  components: {
    name: 'business-components',
    test: /[\\/]src[\\/]components[\\/]/,
    priority: 7
  }
}
```

#### 环境配置
- **开发环境**: `npm run serve:dev`
- **生产环境**: `npm run serve:prod` / `npm run build:prod`
- **打包分析**: `npm run analyzer`

## 浏览器兼容性

```json
"browserslist": [
  "> 1%",
  "last 2 versions", 
  "not ie <= 8"
]
```

支持现代浏览器，不支持IE8及以下版本。

## 部署方式

项目支持Docker部署，提供了Dockerfile和DockerfileSimple两种部署配置。
