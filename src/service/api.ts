import BaseService from '@/service/baseService';

class Api extends BaseService {
  /**
   * banner 获取国网sgcc授权
   * @param options
   */
  public getAuthToken(options: any) {
    const $options = {
      data: {
        method: '10001',
        ...options
      }
    };
    return this.post($options);
  }

  /**
   * 活动用户上报，主要用来统计埋点信息-活动参与人数
   *
   */
  public reportUser(options: any) {
    const $options = {
      data: {
        method: '10006',
        ...options
      }
    };
    return this.post($options);
  }

  /**
   * 通用获取任务列表
   *
   */
  public getTaskList(options: any) {
    const $options = {
      data: {
        method: '10003',
        ...options
      }
    };
    return this.post($options);
  }

  /**
   * 通用任务完成列表
   *
   */
  public completeTask(options: any) {
    const $options = {
      data: {
        method: '10004',
        ...options
      }
    };
    return this.post($options);
  }

  /**锦鲤活动**/

  /**
   * 获取活动列表
   *
   */
  public getActivityList(options: any) {
    const $options = {
      data: {
        method: '10002',
        ...options
      }
    };
    return this.post($options);
  }

   /**
     * 轮次信息
     *
     */

   public getActivityRound(options: any) {
     const $options = {
       data: {
         method: '10019',
         ...options
       }
     }
     return this.post($options);
   }

  /**
   * 获取每个轮次的抽奖码信息
   *
   */
  public getActivityCode(options: any) {
    const $options = {
      data: {
        method: '10005',
        ...options
      }
    };
    return this.post($options);
  }
  /**锦鲤活动**/
}

export const userService = new Api();
