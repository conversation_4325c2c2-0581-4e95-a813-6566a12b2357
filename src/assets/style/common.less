@import "./var.less";

// 字体颜色
.font-color-brand {
  color: @color-brand;
}

.font-color-333 {
  color: @color-first;
}

.font-color-ccc {
  color: @color-third;
}

.font-color-white {
  color: @color-white;
}

.font-color-999 {
  color: @color-second;
}

.font-color-red {
  color: @color-red;
}

// 背景颜色
.background-color-f5f5f5 {
  background-color: @color-bg;
}

.background-color-active {
  background-color: #e7f1ff;
}

.background-color-white {
  background-color: @color-white;
}

// flex 布局
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-align-items {
  align-items: center;
}

.flex-justify-content {
  justify-content: center;
}


// font-weight
.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

// arrow


// text-align
.text-align-center {
  text-align: center;
}

.text-align-left {
  text-align: left;
}

.text-align-right {
  text-align: right;
}

// border-radius
.border-radius-small {
  border-radius: @border-radius-small;
}

.border-radius-middle {
  border-radius: @border-radius-middle;
}

.border-radius-large {
  border-radius: @border-radius-large;
}


// 页面容器统一样式
.container {
  min-height: 100vh;
  background: #f7f8fa;
  box-sizing: border-box;
}

// 顶部返回键
.goBack {
  width: 100%;
  padding-top: 0.54rem;
  background-color: transparent;
  z-index: 999;
  display: flex;
  align-items: center;

  .backImg {
    z-index: 2;
    width: 0.5rem;
    height: 0.5rem;
    margin-left: 0.3rem;
    background-repeat: no-repeat;
    background-size: contain;
  }

  .back-tit {
    font-size: 0.36rem;
    margin-left: 0.1rem;
    color: #fff1d9;
  }
}

#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

// .app-root {
//   min-height: 100vh;
//   background: #f5f5f5;
// }

/*禁止页面被长按复制*/
img {
  pointer-events: none;
}

img {
  width: 100%;
  height: auto;
  font-size: 0;
}

.word {
  span,
  strong,
  p {
    font-size: 0.11rem !important;
  }
}

// 收货信息填写页，输入框字体颜色 设置
.fill-receiver {
  .van-field__control {
    color: #646464;
  }
}

// 页面容器统一样式
.container {
  min-height: 100vh;
  background: #f7f8fa;
  position: relative;
}

.font_26 {
  font-size: @font-size-26;
}

.font_24 {
  font-size: @font-size-24;
}

.font_18 {
  font-size: @font-size-18;
}

.font_14 {
  font-size: 0.14rem;
}

.font_12 {
  font-size: 0.12rem;
}

.font_16 {
  font-size: @font-size-16;
}

.font_17 {
  font-size: @font-size-17;
}

.font_20 {
  font-size: @font-size-20;
}

.font_22 {
  font-size: @font-size-22;
}

.font_26 {
  font-size: @font-size-26;
}

.font_28 {
  font-size: @font-size-28;
}

.font_30 {
  font-size: @font-size-30;
}

.font_32 {
  font-size: @font-size-32;
}

.font_36 {
  font-size: 0.36rem;
}

.font_34 {
  font-size: @font-size-34;
}

.font_35 {
  font-size: 0.35rem;
}

.font_38 {
  font-size: @font-size-38;
}

.font_40 {
  font-size: @font-size-40;
}

.font_37 {
  font-size: 0.37rem;
}

.font_50 {
  font-size: 0.5rem;
}

.font_55 {
  font-size: 0.55rem;
}

.font_42 {
  font-size: 0.42rem;
}

.font_100 {
  font-size: 1rem;
}

.font-weight-normal {
  font-weight: normal;
}

.font-weight-bold {
  font-weight: bold;
}

.line-height-43 {
  line-height: 0.43rem;
  letter-spacing: 0em;
}

.line-height-36 {
  line-height: 0.36rem;
  letter-spacing: 0em;
}

.line-height-8{
  font-stretch: normal;
  line-height: 0.08rem;
  letter-spacing: 0px;
}

.common_text_style {
  font-family: Alibaba PuHuiTi;
  line-height: 0.4rem;
  letter-spacing: 0em;
}



.common_text_style_normal {
  font-family: AlibabaPuHuiTi;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;
}

.common_text_style_bold {
  font-family: AlibabaPuHuiTi;
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0em;
}

.flex_column_start {
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
}


.text_align_center {
  text-align: center;
}

.text_align_left {
  text-align: left;
}

.text_align_right {
  text-align: right;
}

.font_25 {
  font-family: AlibabaPuHuiTi;
  font-size: 0.26rem;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.02em;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_center_baseline {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.flex_column {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.flex_center_space {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.common_font_style {
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.02em;
  font-family: AlibabaPuHuiTi;
  color: #646464;
}

.close_w_h{
  width: 0.62rem;
  height: 0.62rem;
}

.common_close{
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.common_dialog_btn_size {
  width: 4.38rem;
  height: 1.01rem;
}
